import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import { v4 as uuid } from 'uuid'
import fs from 'fs'
import * as AWS from 'aws-sdk'
import User from 'App/Models/User'

const { MediaConvert } = require('@aws-sdk/client-mediaconvert')

export const uploadToS3Bucket = async (
  file: any,
  bucket: string,
  userProfile?: string
): Promise<{ key: string; url: string }> => {
  const s3 = new AWS.S3({
    region: process.env.S3_REGION,
    secretAccessKey: process.env.S3_SECRET,
    accessKeyId: process.env.S3_KEY,
  })
  try {
    const { type, subtype, extname } = file
    let mimeType = type + '/' + subtype
    const name = userProfile ? userProfile + '-' + uuid() + '.' + extname : uuid() + '.' + extname

    // console.log(name)
    // console.log(mimeType)
    const readStream = fs.createReadStream(file.tmpPath)
    await s3
      .upload({
        Key: name,
        Bucket: bucket,
        ContentType: mimeType,
        Body: readStream,
        ...(bucket === process.env.S3_BUCKET && { ACL: 'public-read' }),
      })
      .promise()
    let url = `https://${bucket}.s3.amazonaws.com/${name}`
    return {
      key: name,
      url,
    }
  } catch (err) {
    console.log(err)
    return err
  }
}

export const uploadToS3BucketWithFileName = async (
  file: any,
  bucket: string,
  userProfile?: string,
  filename?: string
): Promise<{ key: string; url: string }> => {
  const s3 = new AWS.S3({
    region: process.env.S3_REGION,
    secretAccessKey: process.env.S3_SECRET,
    accessKeyId: process.env.S3_KEY,
  })
  try {
    const { type, subtype, extname } = file
    let mimeType = type + '/' + subtype
    const name = userProfile
      ? userProfile + '-' + filename ?? uuid() + '.' + extname
      : filename ?? uuid() + '.' + extname

    // console.log(name)
    // console.log(mimeType)
    const readStream = fs.createReadStream(file.tmpPath)
    await s3
      .upload({
        Key: name,
        Bucket: bucket,
        ContentType: mimeType,
        Body: readStream,
        ...(bucket === process.env.S3_BUCKET && { ACL: 'public-read' }),
      })
      .promise()
    let url = `https://${bucket}.s3.amazonaws.com/${name}`
    return {
      key: name,
      url,
    }
  } catch (err) {
    console.log(err)
    return err
  }
}

const credentials: AWS.ConfigurationOptions = {
  region: process.env.S3_REGION,
  credentials: {
    accessKeyId: process.env.S3_KEY as string,
    secretAccessKey: process.env.S3_SECRET as string,
  },
}
// Configure AWS credentials and region
AWS.config.update({
  ...credentials,
  correctClockSkew: true,
})
const mediaConvert = new MediaConvert({
  ...credentials,
  endpoint: process.env.MEDIACONVERT_ENDPOINT,
})

const tenEighty = {
  NameModifier: '_1080p',
  VideoDescription: {
    Width: 1920,
    Height: 1080,
    CodecSettings: {
      Codec: 'H_264',
      H264Settings: {
        MaxBitrate: 5000000,
        RateControlMode: 'QVBR',
      },
    },
  },
  AudioDescriptions: [
    {
      CodecSettings: {
        Codec: 'AAC',
        AacSettings: {
          CodingMode: 'CODING_MODE_2_0',
          SampleRate: 48000,
          Bitrate: 96000,
          // Configure AAC settings here
        },
      },
    },
  ],
  ContainerSettings: {
    Container: 'M3U8',
    M3u8Settings: {
      // Configure M3U8 settings here
    },
  },
}
const sevenTwenty = {
  NameModifier: '_720p',
  VideoDescription: {
    Width: 1280,
    Height: 720,
    CodecSettings: {
      Codec: 'H_264',
      H264Settings: {
        MaxBitrate: 3000000,
        RateControlMode: 'QVBR',
      },
    },
  },
  AudioDescriptions: [
    {
      CodecSettings: {
        Codec: 'AAC',
        AacSettings: {
          CodingMode: 'CODING_MODE_2_0',
          SampleRate: 48000,
          Bitrate: 96000,
          // Configure AAC settings here
        },
      },
    },
  ],
  ContainerSettings: {
    Container: 'M3U8',
    M3u8Settings: {
      // Configure M3U8 settings here
    },
  },
}
const fourEighty = {
  NameModifier: '_480p',
  VideoDescription: {
    Width: 640,
    Height: 480,
    CodecSettings: {
      Codec: 'H_264',
      H264Settings: {
        MaxBitrate: 1080000,
        RateControlMode: 'QVBR',
      },
    },
  },
  AudioDescriptions: [
    {
      CodecSettings: {
        Codec: 'AAC',
        AacSettings: {
          CodingMode: 'CODING_MODE_2_0',
          SampleRate: 48000,
          Bitrate: 96000,
          // Configure AAC settings here
        },
      },
    },
  ],
  ContainerSettings: {
    Container: 'M3U8',
    M3u8Settings: {
      // Configure M3U8 settings here
    },
  },
}

export async function createMediaConvertJob(inputS3Url, outputS3Url) {
  const params = {
    // Replace with the appropriate ARN for your MediaConvert service
    Role: `arn:aws:iam::${process.env.AWS_ACCOUNT_ID}:role/service-role/MediaConvert_Default_Role`,
    Settings: {
      OutputGroups: [
        {
          Name: 'HLS Group',
          OutputGroupSettings: {
            Type: 'HLS_GROUP_SETTINGS',
            HlsGroupSettings: {
              SegmentLength: 10, // Segment duration in seconds
              MinSegmentLength: 10,
              Destination: outputS3Url,
            },
          },
          Outputs: [tenEighty, sevenTwenty, fourEighty],
        },
      ],
      Inputs: [
        {
          FileInput: inputS3Url,
          AudioSelectors: {
            'Audio Selector 1': {
              Offset: 0,
              DefaultSelection: 'DEFAULT',
              SelectorType: 'LANGUAGE_CODE',
              ProgramSelection: 1,
              LanguageCode: 'ENM',
            },
          },
        },
      ],
    },
  }

  const response = await mediaConvert.createJob(params)
  // console.log('Job created successfully:', response)
  // @ts-ignore
  return response.Job
}

export default class FilesController {
  public async uploadFile({ response, auth, request }: HttpContextContract) {
    const user = await User.findOrFail(auth?.user?.id)

    const validationSchema = schema.create({
      file: schema.file({
        size: '400mb',
        extnames: [
          'pdf',
          'png',
          'jpg',
          'jpeg',
          'txt',
          'mp4',
          '3gp',
          'heic',
          'mp3',
          'wav',
          'mov',
          'm3u8',
        ],
      }),
      type: schema.enum(['image', 'video', 'audio', 'rive'] as const),
      alt: schema.string.optional(),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    try {
      if (validationData.file) {
        const uploadBucket = await uploadToS3Bucket(
          validationData.file,
          validationData.type === 'video'
            ? process.env.S3_BUCKET_VIDEO ?? ''
            : validationData.type === 'rive'
            ? process.env.S3_BUCKET_RIVE ?? ''
            : 'hummusedu',
          user.email
        )
        if (uploadBucket.url) {
          // const media = await File.create({
          //   src: uploadBucket.url,
          //   type: validationData.type,
          //   alt: validationData.alt,
          // })

          return response.send({
            data: uploadBucket,
            success: true,
          })
        } else {
          return response.status(400).send({ error: 'upload.image.failed', success: false })
        }
      }
    } catch (error) {
      console.log(error)
      return response.status(400).send({
        success: false,
        data: null,
      })
    }
  }

  public async uploadMyFile({ response, request, auth }: HttpContextContract) {
    const validationSchema = schema.create({
      file: schema.file({
        size: '400mb',
        extnames: ['pdf', 'png', 'jpg', 'jpeg', 'txt', 'mp4', '3gp', 'heic', 'mp3', 'wav', 'mov'],
      }),
      type: schema.string.optional({}),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    const user = await User.findOrFail(auth?.user?.id)

    try {
      if (validationData.file) {
        const uploadBucket = await uploadToS3Bucket(
          validationData.file,
          process.env.S3_BUCKET ?? 'hummusedu',
          user.email
        )
        if (uploadBucket?.url) {
          return response.send({ data: uploadBucket.url, success: true })
        } else {
          return response.status(400).send({
            success: false,
            data: null,
          })
        }
      }
    } catch (error) {
      console.log(error)
      return response.status(400).send({
        success: false,
        data: null,
      })
    }
  }
}
