import { androidpublisher, auth } from '@googleapis/androidpublisher'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Database from '@ioc:Adonis/Lucid/Database'
import Bundle from 'App/Models/Bundle'
import Credit from 'App/Models/Credit'
import Preschool from 'App/Models/Preschool'
import Story from 'App/Models/Story'
import StoryOrder from 'App/Models/StoryOrder'
import Transaction from 'App/Models/Transaction'
import Voucher from 'App/Models/Voucher'
import Wallet from 'App/Models/Wallet'
import axios from 'axios'
import BigNumber from 'bignumber.js'
import jwt, { JwtHeader, JwtPayload } from 'jsonwebtoken'
import { DateTime } from 'luxon'

const getAndroidPublisher = async () => {
  const auth2 = new auth.GoogleAuth({
    keyFile: process.env.GOOGLE_IAP,
    scopes: ['https://www.googleapis.com/auth/androidpublisher'],
  })
  const androidPublisher = androidpublisher({ auth: auth2, version: 'v3' })
  return androidPublisher
}

export default class PaymentsController {
  public async waivedPurchase({ request, response, auth }: HttpContextContract) {
    try {
      const user = await auth.authenticate()
      const wallet = await Wallet.findByOrFail('user_id', user.id)
      const validationSchema = schema.create({
        story_id: schema.number.optional([
          rules.exists({ table: 'stories', column: 'id' }),
          rules.requiredIfNotExists('preschool_id'),
        ]),
        preschool_id: schema.number.optional([
          rules.exists({ table: 'preschools', column: 'id' }),
          rules.requiredIfNotExists('story_id'),
        ]),
        voucher_id: schema.number.optional(),
      })

      const validationData = await request.validate({
        schema: validationSchema,
      })

      let findVoucher: Voucher | null
      const findStory = await Story.find(validationData.story_id ?? 0)
      const findPreschool = await Preschool.find(validationData.preschool_id ?? 0)
      if (validationData.voucher_id) {
        findVoucher = await Voucher.query()
          .where('id', validationData.voucher_id)
          .where((query) => {
            query
              .whereNull('expired_at')
              .orWhere('expired_at', '>=', DateTime.local().toFormat('yyyy-MM-dd HH:mm:ss'))
          })
          .withCount('storyOrders', (query) => {
            query.countDistinct('user_id').as('total_of_used')
          })
          .first()
        if (!findVoucher) {
          return response.status(400).send({
            success: false,
            message: 'Invalid voucher or voucher expired.',
          })
        }

        if (findVoucher.maxOfUsed !== null) {
          // let firstTimeUsed = true
          // if (findPreschool) {
          //   const checkOrders = await StoryOrder.query()
          //     .where('user_id', user.id)
          //     .where('voucher_id', findVoucher.id)

          //   if (checkOrders.length > 0) {
          //     firstTimeUsed = false
          //   }
          // }
          // if (findVoucher.totalOfUsed >= findVoucher.maxOfUsed && firstTimeUsed) {
          if (findVoucher.totalOfUsed >= findVoucher.maxOfUsed) {
            return response.status(400).send({
              success: false,
              message: 'The voucher usage limit has been reached.',
            })
          }
        }

        const purchaseFee = new BigNumber(findStory?.price ?? 0)
          .multipliedBy(1 - findVoucher.discount)
          .decimalPlaces(2)
          .toNumber()
        if (purchaseFee !== 0) {
          return response
            .status(400)
            .send({ success: false, message: 'Please proceed with in-app-purchase' })
        }
      } else {
        if (findStory && findStory.price > 0) {
          return response.status(400).send({
            success: false,
            message: 'Please proceed with in-app-purchase',
          })
        }
      }

      const result = await Database.transaction(async (trx) => {
        if (findStory) {
          const findOrder = await StoryOrder.query()
            .where('story_id', findStory.id)
            .where('user_id', user.id)
            .first()
          if (findOrder) {
            return null
          }
          const newTrx = new Transaction()
          newTrx.walletId = wallet.id
          newTrx.userId = user.id
          newTrx.title = 'Redeemed Story'
          newTrx.description = findStory.title
          newTrx.type = 'purchase'
          newTrx.status = 'confirmed'
          newTrx.amount = 0
          newTrx.amountIn = 0
          newTrx.amountOut = 0
          newTrx.remark = findVoucher ? `Voucher used - ${findVoucher!.voucherCode}` : 'Free story'
          newTrx.useTransaction(trx)
          await newTrx.save()

          const newOrder = new StoryOrder()
          newOrder.storyId = findStory.id
          if (findVoucher) {
            newOrder.voucherId = findVoucher.id
          }
          newOrder.userId = user.id
          newOrder.finalAmount = 0
          newOrder.discountAmount = findStory.price

          newOrder.useTransaction(trx)
          await newOrder.save()

          return [newOrder]
        } else if (findPreschool) {
          const findStories = await Story.query()
            .where('preschool_id', findPreschool.id)
            .whereNot((query) =>
              query.whereHas('storyOrders', (query) => query.where('user_id', user.id))
            )

          const unlockedStories: StoryOrder[] = []
          for (const story of findStories) {
            const newTrx = new Transaction()
            newTrx.walletId = wallet.id
            newTrx.userId = user.id
            newTrx.title = 'Redeemed Story'
            newTrx.description = story.title
            newTrx.type = 'purchase'
            newTrx.status = 'confirmed'
            newTrx.amount = 0
            newTrx.amountIn = 0
            newTrx.amountOut = 0
            newTrx.remark = findVoucher
              ? `Voucher used - ${findVoucher!.voucherCode}`
              : 'Free story'
            newTrx.useTransaction(trx)
            await newTrx.save()

            const newOrder = new StoryOrder()
            newOrder.storyId = story.id
            if (findVoucher) {
              newOrder.voucherId = findVoucher.id
            }
            newOrder.userId = user.id
            newOrder.finalAmount = 0
            newOrder.discountAmount = story.price

            newOrder.useTransaction(trx)
            await newOrder.save()

            unlockedStories.push(newOrder)
          }

          return unlockedStories
        }

        return null
      })

      return response.status(200).send({ success: true, data: result })
    } catch (error) {
      console.log(error)
    }
  }

  public async validatePurchaseAndroid({ request, response, auth }: HttpContextContract) {
    try {
      console.log('validate android purchase')
      console.log(request.all())
      const user = await auth.authenticate()
      const validationSchema = schema.create({
        purchase_token: schema.string(),
        handle: schema.string([
          rules.exists({
            table: 'stories',
            column: 'handle',
            where: { is_community: true },
          }),
        ]),
      })

      const validationData = await request.validate({ schema: validationSchema })
      const findStory = await Story.findByOrFail('handle', validationData.handle)
      const play = await getAndroidPublisher()
      const { data: productPurchase } = await play.purchases.products.get({
        // Mobile app package name
        packageName: process.env.GOOGLE_PACKAGE_NAME,
        // sku product
        productId: findStory.handle,
        token: validationData.purchase_token,
      })

      console.log('productPurchase: ', productPurchase)

      if (productPurchase.purchaseState === 0) {
        const result = await Database.transaction(async (trx) => {
          const newOrder = new StoryOrder()
          newOrder.storyId = findStory.id
          // newOrder.voucherId = findVoucher.id
          newOrder.userId = user.id
          newOrder.finalAmount = findStory.price
          newOrder.discountAmount = 0

          newOrder.useTransaction(trx)
          await newOrder.save()
          return newOrder
        })

        return response.status(200).send({
          success: true,
          data: result,
        })
      }

      return response.status(400).send({
        success: false,
        message: 'Purchase not completed.',
      })
    } catch (error) {
      console.log(error)
      return response.status(400).send({
        success: false,
        error,
      })
    }
  }

  public async validatePurchaseIOS({ request, response, auth }: HttpContextContract) {
    try {
      console.log('validate ios purchase')
      console.log(request.all())
      const user = await auth.authenticate()
      const validationSchema = schema.create({
        transaction_id: schema.string(),
        handle: schema.string([
          rules.exists({
            table: 'stories',
            column: 'handle',
            where: { is_community: true },
          }),
        ]),
      })

      const validationData = await request.validate({ schema: validationSchema })
      const findStory = await Story.findByOrFail('handle', validationData.handle)

      const now = DateTime.local()
      const jwtHeaders: JwtHeader = {
        alg: 'ES256',
        kid: process.env.IOS_API_KEY,
        typ: 'JWT',
      }

      const jwtPayload: JwtPayload = {
        iss: process.env.IOS_JWT_ISS,
        iat: now.toUnixInteger(),
        exp: now.plus({ minute: 3 }).toUnixInteger(),
        aud: 'appstoreconnect-v1',
        bid: process.env.IOS_BUNDLE_ID,
      }

      const jwtToken = jwt.sign(jwtPayload, process.env.IOS_PRIVATE_KEY as string, {
        algorithm: 'ES256',
        header: jwtHeaders,
      })

      const { data: transactionResponse, status } = await axios.get(
        `${process.env.IOS_STOREKIT_BASE_URL}${validationData.transaction_id}`,
        {
          headers: {
            Authorization: `Bearer ${jwtToken}`,
          },
        }
      )

      if (status !== 200) {
        return response.status(400).send({
          success: false,
          message: 'Failed to get transaction.',
        })
      }

      const decodedPayload: any = jwt.decode(transactionResponse.signedTransactionInfo)
      console.log('IOS: ', decodedPayload)

      // product validation
      if (decodedPayload.inAppOwnershipType !== 'PURCHASED') {
        return response.status(400).send({
          success: false,
          message: 'Not yet purchased',
        })
      }

      const result = await Database.transaction(async (trx) => {
        const newOrder = new StoryOrder()
        newOrder.storyId = findStory.id
        // newOrder.voucherId = findVoucher.id
        newOrder.userId = user.id
        newOrder.finalAmount = findStory.price
        newOrder.discountAmount = 0

        newOrder.useTransaction(trx)
        await newOrder.save()
        return newOrder
      })

      return response.status(200).send({
        success: true,
        data: result,
      })

      // return response.status(400).send({
      //   success: false,
      //   message: 'Purchase not completed.',
      // })
    } catch (error) {
      console.log(error)
      return response.status(400).send({
        success: false,
        error,
      })
    }
  }

  public async validatePurchaseCreditsAndroid({ request, response, auth }: HttpContextContract) {
    try {
      console.log('validate android purchase credits')
      console.log(request.all())
      const user = await auth.authenticate()
      const wallet = await Wallet.findByOrFail('user_id', user.id)
      const validationSchema = schema.create({
        purchase_token: schema.string(),
        handle: schema.string([
          rules.exists({
            table: 'credits',
            column: 'handle',
          }),
        ]),
      })

      const validationData = await request.validate({ schema: validationSchema })
      const findCredit = await Credit.findByOrFail('handle', validationData.handle)
      const play = await getAndroidPublisher()
      const { data: productPurchase } = await play.purchases.products.get({
        // Mobile app package name
        packageName: process.env.GOOGLE_PACKAGE_NAME,
        // sku product
        productId: findCredit.handle,
        token: validationData.purchase_token,
      })

      console.log('productPurchase: ', productPurchase)

      if (productPurchase.purchaseState === 0) {
        const result = await Database.transaction(async (trx) => {
          const newTrx = new Transaction()
          newTrx.walletId = wallet.id
          newTrx.userId = user.id
          newTrx.title = 'Top-up'
          newTrx.description = 'From Credit / Debit Card'
          newTrx.amount = findCredit.amount
          newTrx.amountIn = findCredit.amount
          newTrx.amountOut = 0
          newTrx.type = 'deposit'
          newTrx.status = 'confirmed'
          newTrx.remark = findCredit.handle

          newTrx.useTransaction(trx)
          await newTrx.save()
          return newTrx
        })

        return response.status(200).send({
          success: true,
          data: result,
        })
      }

      return response.status(400).send({
        success: false,
        message: 'Purchase not completed.',
      })
    } catch (error) {
      console.log(error)
      return response.status(400).send({
        success: false,
        error,
      })
    }
  }

  public async validatePurchaseCreditsIOS({ request, response, auth }: HttpContextContract) {
    try {
      console.log('validate ios purchase credits')
      console.log(request.all())
      const user = await auth.authenticate()
      const wallet = await Wallet.findByOrFail('user_id', user.id)
      const validationSchema = schema.create({
        transaction_id: schema.string(),
        handle: schema.string([
          rules.exists({
            table: 'credits',
            column: 'handle',
          }),
        ]),
      })

      const validationData = await request.validate({ schema: validationSchema })
      const findCredit = await Credit.findByOrFail('handle', validationData.handle)

      const now = DateTime.local()
      const jwtHeaders: JwtHeader = {
        alg: 'ES256',
        kid: process.env.IOS_API_KEY,
        typ: 'JWT',
      }

      const jwtPayload: JwtPayload = {
        iss: process.env.IOS_JWT_ISS,
        iat: now.toUnixInteger(),
        exp: now.plus({ minute: 3 }).toUnixInteger(),
        aud: 'appstoreconnect-v1',
        bid: process.env.IOS_BUNDLE_ID,
      }

      const jwtToken = jwt.sign(jwtPayload, process.env.IOS_PRIVATE_KEY as string, {
        algorithm: 'ES256',
        header: jwtHeaders,
      })

      const { data: transactionResponse, status } = await axios.get(
        `${process.env.IOS_STOREKIT_BASE_URL}${validationData.transaction_id}`,
        {
          headers: {
            Authorization: `Bearer ${jwtToken}`,
          },
        }
      )

      if (status !== 200) {
        return response.status(400).send({
          success: false,
          message: 'Failed to get transaction.',
        })
      }

      const decodedPayload: any = jwt.decode(transactionResponse.signedTransactionInfo)
      console.log('IOS: ', decodedPayload)

      // product validation
      if (decodedPayload.inAppOwnershipType !== 'PURCHASED') {
        return response.status(400).send({
          success: false,
          message: 'Purchase not completed',
        })
      }

      const result = await Database.transaction(async (trx) => {
        const newTrx = new Transaction()
        newTrx.walletId = wallet.id
        newTrx.userId = user.id
        newTrx.title = 'Top-up'
        newTrx.description = 'From Credit / Debit Card'
        newTrx.amount = findCredit.amount
        newTrx.amountIn = findCredit.amount
        newTrx.amountOut = 0
        newTrx.type = 'deposit'
        newTrx.status = 'confirmed'
        newTrx.remark = findCredit.handle

        newTrx.useTransaction(trx)
        await newTrx.save()
        return newTrx
      })

      return response.status(200).send({
        success: true,
        data: result,
      })
    } catch (error) {
      console.log(error)
      return response.status(400).send({
        success: false,
        error,
      })
    }
  }

  public async validatePurchaseWithCredit({ request, response, auth }: HttpContextContract) {
    try {
      console.log('validate story purchase')
      const user = await auth.authenticate()
      const validationSchema = schema.create({
        bundle_id: schema.number.optional([
          rules.exists({ table: 'bundles', column: 'id' }),
          rules.requiredIfNotExists('story_id'),
        ]),
        story_id: schema.number.optional([
          rules.exists({ table: 'stories', column: 'id' }),
          rules.requiredIfNotExists('bundle_id'),
        ]),
      })

      const validationData = await request.validate({ schema: validationSchema })
      const { story_id, bundle_id } = validationData
      const wallet = await Wallet.query()
        .where('user_id', user.id)
        .withCount('transactions', (query) => {
          query.sum('amount').as('balance')
        })
        .firstOrFail()

      if (story_id) {
        const findStory = await Story.findOrFail(story_id)
        // check if story already bought
        const isOrderExists = await StoryOrder.query()
          .where('user_id', user.id)
          .where('story_id', findStory.id)
          .first()

        if (isOrderExists) {
          return response.ok({ success: true, message: 'The story already redeemed.' })
        }

        if (wallet.balance > findStory.price) {
          const result = await Database.transaction(async (trx) => {
            const newTrx = new Transaction()
            newTrx.walletId = wallet.id
            newTrx.userId = user.id
            newTrx.title = 'Redeemed Story'
            newTrx.description = findStory.title
            newTrx.amount = 0 - findStory.price
            newTrx.amountIn = 0
            newTrx.amountOut = findStory.price
            newTrx.type = 'purchase'
            newTrx.status = 'confirmed'
            newTrx.remark = `sales price - ${findStory.price}, compare at price - ${findStory.compareAtPrice}`
            newTrx.useTransaction(trx)
            await newTrx.save()

            const newOrder = new StoryOrder()
            newOrder.storyId = findStory.id
            newOrder.userId = user.id
            newOrder.finalAmount = findStory.price
            newOrder.discountAmount = 0

            newOrder.useTransaction(trx)
            await newOrder.save()
            return newOrder
          })

          return response.status(200).send({
            success: true,
            data: result,
          })
        }
      } else if (bundle_id) {
        const findBundle = await Bundle.query()
          .where('id', bundle_id)
          .preload('stories', (query) =>
            query.withCount('storyOrders', (query) =>
              query
                .where('user_id', user.id)
                // .where('blocked', false) // TODO: find the storyOrders with blocked, and set blocked to false
                .as('redeemed')
            )
          )
          .firstOrFail()

        if (findBundle.price > 0 && (findBundle.stories?.length ?? 0) > 0) {
          if (wallet.balance > findBundle.price) {
            const storiesTitle: string[] = []
            // get the stories that not yet redeemed
            const unlockStories = findBundle.stories.filter((story) => {
              if (!story.hasRedeemed) {
                storiesTitle.push(story.title)
                return true
              }

              return false
            })

            await Database.transaction(async (trx) => {
              const newTrx = new Transaction()
              newTrx.walletId = wallet.id
              newTrx.userId = user.id
              newTrx.title = `Redeemed Bundle (${findBundle.id})`
              newTrx.description = findBundle.title
              newTrx.amount = 0 - findBundle.price
              newTrx.amountIn = 0
              newTrx.amountOut = findBundle.price
              newTrx.type = 'purchase'
              newTrx.status = 'confirmed'
              newTrx.remark = `${findBundle.title} - ${findBundle.price}, discount - ${
                findBundle.discount
              }, stories - ${storiesTitle.join(',')}`
              newTrx.useTransaction(trx)
              await newTrx.save()

              for (let story of unlockStories) {
                const finalAmount = new BigNumber(story.price * (1 - findBundle.discount))
                  .decimalPlaces(2)
                  .toNumber()
                const newOrder = new StoryOrder()
                newOrder.storyId = story.id
                newOrder.userId = user.id
                newOrder.finalAmount = finalAmount
                newOrder.discountAmount = story.price - finalAmount

                newOrder.useTransaction(trx)
                await newOrder.save()
              }
            })

            return response.ok({ success: true })
          } else {
            return response.badRequest({ success: false, message: 'Not enough credits to unlock.' })
          }
        } else {
          return response.ok({
            success: false,
            message: 'You already owned all the stories in this bundle.',
          })
        }
      }

      return response.badRequest({ success: false, message: 'Invalid access' })
    } catch (error) {
      console.log(error)
      return response.status(400).send({
        success: false,
        error,
      })
    }
  }
}
