import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import Event from '@ioc:Adonis/Core/Event'
import RecordingResult from 'App/Models/RecordingResult'
import { uploadToS3Bucket } from './FilesController'
import File from 'App/Models/File'
import Chapter from 'App/Models/Chapter'
import _ from 'lodash'

export default class RecordingResultsController {
  public async find({ response, request }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = _.omit(request.all(), ['page', 'sort', 'limit'])

    const recordingResults = await RecordingResult.filter(filters)
      .preload('user')
      .preload('story')
      .preload('chapter')
      .preload('file')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.status(200).send(recordingResults)
  }

  public async findOne({ response, params }: HttpContextContract) {
    const recordingResult = await RecordingResult.query()
      .where('id', params.id)
      .preload('file')
      .preload('user')
      .preload('chapter', (query) => query.preload('story'))
      .firstOrFail()

    return response.status(200).send({
      data: recordingResult,
    })
  }

  public async create({ auth, response, request }: HttpContextContract) {
    const user = await auth.authenticate()
    const validationSchema = schema.create({
      file: schema.file.optional({
        size: '200mb',
        extnames: ['wav'],
      }),
      category: schema.array().members(schema.string.optional()),
      device: schema.string.optional(),
      user_token: schema.string.optional(),
      language: schema.string.optional(),
      chapter_id: schema.number(),
      calibration: schema.array().members(schema.string()),
      minimum_volume: schema.string(),
      actual_volume: schema.array().members(schema.string()),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    const findChapter = await Chapter.query()
      .where('id', validationData.chapter_id)
      .preload('story')
      .firstOrFail()

    let recordingFile: File | null = null

    // TODO: set as private
    if (validationData.file) {
      const uploadBucket = await uploadToS3Bucket(
        validationData.file,
        process.env.S3_bucket ?? 'hummusedu',
        user.email
      )

      // upload to s3 for backup
      recordingFile = await File.create({
        src: uploadBucket.url,
        type: 'recording',
        userId: user.id,
      })
    }

    // first start activity
    if (findChapter.story.isCommunity && findChapter.story.defaultChapterId === findChapter.id) {
      const findRecording = await RecordingResult.query()
        .where('story_id', findChapter.storyId)
        .where('chapter_id', findChapter.id)
        .where('user_id', user.id)
        .first()

      if (!findRecording) {
        // trigger first start event
        Event.emit('activity:start', {
          email: user.email,
        })
      }
    }

    let categories = validationData.category?.join(', ')
    const recordingResponse = await RecordingResult.create({
      fileId: recordingFile?.id,
      category: categories ?? '',
      device: validationData.device,
      language: validationData.language,
      userId: user.id,
      chapterId: validationData.chapter_id,
      storyId: findChapter.storyId,
      calibrations: validationData.calibration,
      volumes: validationData.actual_volume,
      minimumVolume: validationData.minimum_volume,
    })

    return response.status(200).send({
      data: recordingResponse,
    })
  }
}
