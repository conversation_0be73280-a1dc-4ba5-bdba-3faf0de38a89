import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Database from '@ioc:Adonis/Lucid/Database'
import Story, { StoryType } from 'App/Models/Story'
import StoryOrder from 'App/Models/StoryOrder'
import User from 'App/Models/User'
import _, { keys } from 'lodash'

export default class StoryOrdersController {
  public async findAll({ auth, request, response }: HttpContextContract) {
    const user = await User.find(auth.user?.id ?? 0)
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = _.omit(request.all(), ['page', 'limit', 'sort'])
    if (!keys(filters).includes('region')) {
      filters['region'] = 'sg'
    }

    // if logged in return user-preloaded packs
    // otherwise return all packs
    if (user === null) {
      const storyOrders = await StoryOrder.filter(filters)
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.status(200).send(storyOrders)
    }

    const storyOrders = await StoryOrder.filter(filters)
      .where('user_id', user.id)
      .where('blocked', false)
      .as('redeemed')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.status(200).send(storyOrders)
  }

  public async findOne({ auth, response, params }: HttpContextContract) {
    const user = await User.find(auth.user?.id ?? 0)

    // if logged in return user-preloaded pack
    // otherwise return normal pack
    if (user === null) {
      const storyOrder = await StoryOrder.query()
        .where('id', params.id)
        .where('blocked', false)
        .first()
      return response.status(200).send({ data: storyOrder })
    }

    const storyOrder = await StoryOrder.query()
      .where('id', params.id)
      .where('user_id', user.id)
      .where('blocked', false)
      .as('redeemed')
      .first()

    return response.send({
      data: storyOrder,
    })
  }

  public async findUserStoryRedemption({ request, response, params }: HttpContextContract) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'id:asc').split(':')
      const filters = _.omit(request.all(), ['page', 'limit', 'sort'])
      const storyOrders = await StoryOrder.filter(filters)
        .where('block', false)
        .whereHas('story', (query) => {
          query.where('id', params.id)
        })
        .preload('user')
        .preload('story')
        .preload('voucher')
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.status(200).send(storyOrders)
    } catch (error) {
      console.log(error)
    }
  }

  public async findUserPreschoolRedemption({ request, response, params }: HttpContextContract) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'id:asc').split(':')
      const filters = _.omit(request.all(), ['page', 'limit', 'sort'])
      const storyOrders = await StoryOrder.filter(filters)
        .where('block', false)
        .whereHas('story', (query) => {
          query.whereHas('preschool', (query) => query.where('id', params.id))
        })
        .preload('user')
        .preload('story')
        .preload('voucher')
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.status(200).send(storyOrders)
    } catch (error) {
      console.log(error)
    }
  }

  public async updateUserPreschoolBlocked({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      user_id: schema.number([rules.exists({ column: 'id', table: 'users' })]),
      preschool_id: schema.number([rules.exists({ column: 'id', table: 'preschools' })]),
      blocked: schema.boolean(),
    })
    const validationData = await request.validate({ schema: validationSchema })

    await Database.transaction(async (trx) => {
      if (validationData.blocked) {
        const storyOrders = await StoryOrder.query()
          .where('user_id', validationData.user_id)
          .whereHas('story', (query) => query.where('preschool_id', validationData.preschool_id))

        for (let storyOrder of storyOrders) {
          storyOrder.blocked = true

          storyOrder.useTransaction(trx)
          await storyOrder.save()
        }
      } else {
        const stories = await Story.query()
          .where('preschool_id', validationData.preschool_id)
          .where('type', StoryType.PRESCHOOL)
          .where('status', 'active')

        await StoryOrder.updateOrCreateMany(
          ['storyId', 'userId'],
          stories.map((story) => ({
            storyId: story.id,
            userId: validationData.user_id,
            finalAmount: 0,
            discountAmount: 0,
            blocked: false,
          })),
          { client: trx }
        )
      }
    })

    return response.ok({ success: true })
  }
}
