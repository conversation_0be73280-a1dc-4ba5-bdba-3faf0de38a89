import { planQueue, PlanJobKeys } from 'App/Listeners/PlanQueue'
import Plan from 'App/Models/Plan'
import PlanPricing from 'App/Models/PlanPricing'
import StoryOrder from 'App/Models/StoryOrder'
import Subscription from 'App/Models/Subscription'
import Transaction from 'App/Models/Transaction'
import User from 'App/Models/User'
import { Job } from 'bullmq'
import { DateTime } from 'luxon'
import stripe from 'stripe'

export default class PlanBullJobs {
  public static async registerJobs() {
    console.log('PlanBullJobs register')
    planQueue.worker(PlanBullJobs.processJob)
  }

  private static async processJob(job: Job) {
    console.log(`Processing job ${job.id} of type ${job.name}`)

    switch (job.name) {
      case PlanJobKeys.PRODUCT_CREATED:
        await PlanBullJobs.onStripeProductCreated(job)
        break
      case PlanJobKeys.PRICE_CREATED:
        await PlanBullJobs.onStripePriceCreated(job)
        break
      case PlanJobKeys.PRODUCT_UPDATED:
        await PlanBullJobs.onStripeProductUpdated(job)
        break
      case PlanJobKeys.PRICE_UPDATED:
        await PlanBullJobs.onStripePriceUpdated(job)
        break
      case PlanJobKeys.PRODUCT_DELETED:
        await PlanBullJobs.onStripeProductDeleted(job)
        break
      case PlanJobKeys.PRICE_DELETED:
        await PlanBullJobs.onStripePriceDeleted(job)
        break
      case PlanJobKeys.CUSTOMER_CREATED:
        await PlanBullJobs.onCustomerCreated(job)
        break
      case PlanJobKeys.CUSTOMER_UPDATED:
        await PlanBullJobs.onCustomerUpdated(job) // orders 1.
        break
      case PlanJobKeys.SUBSCRIPTION_CREATED:
        await PlanBullJobs.onSubscriptionCreated(job) // orders 2.
        break
      case PlanJobKeys.SUBSCRIPTION_UPDATED:
        await PlanBullJobs.onSubscriptionUpdated(job) // orders 3.
        break
      case PlanJobKeys.SUBSCRIPTION_DELETED:
        await PlanBullJobs.onSubscriptionDeleted(job)
        break
      case PlanJobKeys.INVOICE_PAID:
        await PlanBullJobs.onPurchased(job) // orders 4a.
        break
      case PlanJobKeys.INVOICE_FAILED:
        await PlanBullJobs.onFailed(job) // orders 4b.
        break
      default:
        console.log(`Unknown job type: ${job.name}`)
    }
  }

  private static async onStripeProductCreated(job) {
    const product: stripe.Product = job.data
    console.log('plan.onStripeProductCreated', product)

    const plan = await Plan.query().where('stripe_product_id', product.id).first()
    if (plan) {
      return
    }

    await Plan.create({ title: product.name, stripeProductId: product.id })
  }

  private static async onStripePriceCreated(job) {
    const price: stripe.Price = job.data
    console.log('plan.onStripePriceCreated', price)

    const plan = await Plan.findByOrFail('stripe_product_id', price.product as string)

    await PlanPricing.updateOrCreate(
      { stripePriceId: price.id, planId: plan.id },
      {
        stripePriceId: price.id,
        planId: plan.id,
        price: (price.unit_amount ?? 0) / 100,
        duration:
          price.recurring?.interval === 'month'
            ? 'month'
            : price.recurring?.interval === 'year'
              ? 'year'
              : undefined,
      }
    )
  }

  private static async onStripeProductUpdated(job) {
    const product: stripe.Product = job.data
    console.log('plan.onStripeProductUpdated', product)

    const plan = await Plan.query().where('stripe_product_id', product.id).first()
    // plan should not be null
    if (!plan) {
      return
    }

    plan.title = product.name

    await plan.save()
  }

  private static async onStripePriceUpdated(job) {
    const price: stripe.Price = job.data
    console.log('plan.onStripePriceUpdated', price)
    const plan = await Plan.findByOrFail('stripe_product_id', price.product as string)

    await PlanPricing.updateOrCreate(
      { stripePriceId: price.id, planId: plan.id },
      {
        stripePriceId: price.id,
        planId: plan.id,
        price: (price.unit_amount ?? 0) / 100,
        duration:
          price.recurring?.interval === 'month'
            ? 'month'
            : price.recurring?.interval === 'year'
              ? 'year'
              : undefined,
        blocked: false,
      }
    )
  }

  private static async onStripeProductDeleted(job) {
    const product: stripe.Product = job.data
    console.log('plan.onStripeProductDeleted', product)
    const plan = await Plan.findByOrFail('stripe_product_id', product.id)
    plan.blocked = true
    await plan.save()

    // const planPricing = await PlanPricing.query().where('stripeProductId', product.id).first()

    // // planPricing should not be null
    // if (!planPricing) {
    //   console.log('planPricing.onStripeProductDeleted', 'unable to find product')
    //   return
    // }

    // // planPricing.stripeProductId = null
    // // block this because stripe product is invalid
    // planPricing.blocked = true
    // await planPricing.save()
  }

  private static async onStripePriceDeleted(job) {
    const price: stripe.Price = job.data
    console.log('plan.onStripePriceDeleted', price)

    const planPricing = await PlanPricing.query().where('stripe_price_id', price.id).first()

    // planPricing should not be null
    if (!planPricing) {
      return
    }

    planPricing.blocked = true
    await planPricing.save()
  }

  private static async onCustomerCreated(job) {
    const customer: stripe.Customer = job.data
    console.log('plan.onCustomerCreated', customer)

    if (customer.email === null) {
      return
    }

    let user = await User.query().where('email', customer.email!).first()

    if (!user) {
      console.log('plan.onCustomerCreated', 'user not found via email')

      // email not exist, need to create user
      // preferably not
      user = await User.create({
        stripeCustomerId: customer.id,
        email: customer.email!,
        name: customer.name ?? undefined,
      })
    }

    // link stripe customer to user
    user.stripeCustomerId = customer.id
    await user.save()
  }

  private static async onCustomerUpdated(job) {
    const customer: stripe.Customer = job.data
    console.log('plan.onCustomerUpdated', customer)

    if (customer.email === null) {
      return
    }

    // let user = await User.query().where('email', customer.email!).first()

    let user = await User.query().where('stripe_customer_id', customer.id).first()

    if (!user) {
      console.log('plan.onCustomerUpdated', 'user not found via stripe customer id')

      user = await User.query().where('email', customer.email!).first()

      if (!user) {
        console.log('plan.onCustomerUpdated', 'user not found via email')

        user = await User.query().where('email', customer.email!).first()

        // email not exist, need to create user
        // preferably not
        user = await User.create({
          stripeCustomerId: customer.id,
          email: customer.email!,
          name: customer.name ?? undefined,
        })
      }
    }

    // link stripe customer to user
    user.stripeCustomerId = customer.id
    await user.save()
  }

  private static async onSubscriptionCreated(job) {
    const subscription: stripe.Subscription = job.data
    console.log('plan.onSubscriptionCreated', subscription)

    let user = await User.query()
      .where('stripe_customer_id', subscription.customer as string)
      .first()

    if (!user) {
      console.log('plan.onSubscriptionCreated', 'user not found via stripe user id')
      // TODO
      throw new Error('User not found')
    }

    const findPlanPricing = await PlanPricing.query()
      .where('stripe_price_id', subscription.items.data[0].price.id)
      .preload('plan')
      .first()

    if (!findPlanPricing) {
      console.log('plan.onSubscriptionCreated', 'plan pricing not found via stripe price id')

      throw new Error('Plan not found')
    }

    // create subscription
    await Subscription.create({
      userId: user.id,
      planId: findPlanPricing.planId,
      planPricingId: findPlanPricing.id,
      provider: 'stripe',
      providerUserId: subscription.customer as string,
      providerSubscriptionId: subscription.id,
      providerPlanId: findPlanPricing.stripePriceId as string,
      status: subscription.status,
      startDate: DateTime.fromSeconds(subscription.start_date),
      cycleStartDate: DateTime.fromSeconds(subscription.current_period_start),
      cycleEndDate: DateTime.fromSeconds(subscription.current_period_end),
    })

    // create all story order here
    // const plan = await Plan.query()
    //   .where('id', findPlanPricing.planId)
    //   .preload('planStories')
    //   .first()

    // if (!plan) {
    //   console.log('plan.onSubscriptionCreated', 'plan not found via plan id')

    //   // NOTE: should not happen
    //   return
    // }

    // for (let story of plan.planStories) {
    //   await StoryOrder.updateOrCreate(
    //     {
    //       storyId: story.id,
    //       userId: user.id,
    //     },
    //     {
    //       storyId: story.id,
    //       userId: user.id,
    //       blocked: false,
    //     }
    //   )
    // }
  }

  private static async onSubscriptionUpdated(job) {
    const subscription: stripe.Subscription = job.data
    console.log('plan.onSubscriptionUpdated', subscription)

    let user = await User.query()
      .where('stripe_customer_id', subscription.customer as string)
      .first()

    if (!user) {
      console.log('plan.onSubscriptionUpdated', 'user not found via stripe user id')
      // TODO
      throw new Error('User not found')
    }

    // find subscription
    const findSubscription = await Subscription.query()
      .where('provider_subscription_id', subscription.id)
      .first()

    if (!findSubscription) {
      console.log('plan.onSubscriptionUpdated', 'subscription not found via subscription id')

      throw new Error('Subscription not found')
    }

    // update subscription
    findSubscription.merge({
      // userId: user.id,
      // planId: findPlan.id,
      // provider: 'stripe',
      // providerUserId: subscription.customer as string,
      // providerSubscriptionId: subscription.id,
      // providerPlanId: findPlan.stripePriceId,
      status: subscription.status,
      startDate: DateTime.fromSeconds(subscription.start_date),
      // endDate: DateTime.fromSeconds(subscription.ended_at),
      cycleStartDate: DateTime.fromSeconds(subscription.current_period_start),
      cycleEndDate: DateTime.fromSeconds(subscription.current_period_end),
    })
    await findSubscription.save()
  }

  private static async onSubscriptionDeleted(job) {
    const subscription: stripe.Subscription = job.data
    console.log('plan.onSubscriptionDeleted', subscription)

    let user = await User.query()
      .where('stripe_customer_id', subscription.customer as string)
      .first()

    if (!user) {
      // TODO
      throw new Error('User not found')
    }

    // find subscription
    const findSubscription = await Subscription.query()
      .where('provider_subscription_id', subscription.id)
      .first()

    if (!findSubscription) {
      throw new Error('Subscription not found')
    }

    // update subscription
    findSubscription.merge({
      status: subscription.status,
      endDate: DateTime.now(),
    })
    await findSubscription.save()

    // find all story order & block it
    const plan = await Plan.query()
      .where('id', findSubscription.planId)
      .preload('planStories')
      .first()

    if (!plan) {
      // NOTE: should not happen
      throw new Error('Plan not found')
    }

    for (let story of plan.planStories) {
      await StoryOrder.query()
        .where('user_id', findSubscription.userId)
        .where('story_id', story.id)
        .update({ blocked: true })
    }
  }

  private static async onPurchased(job) {
    const invoice: stripe.Invoice = job.data
    console.log('plan.onPurchased', invoice)

    const user = await User.query()
      .where('stripe_customer_id', invoice.customer as string)
      .orWhere('email', invoice.customer_email ?? 'will-never-trigger')
      .first()

    if (!user) {
      console.log('plan.onPurchased', 'user not found via stripe user id')

      throw new Error('User not found')
    }

    const findSubscription = await Subscription.query()
      .where('provider_subscription_id', invoice.subscription as string)
      .first()

    if (!findSubscription) {
      console.log('plan.onPurchased', 'subscription not found via subscription id')

      throw new Error('Subscription not found')
    }

    await Transaction.create({
      userId: user.id,
      title: 'Subscription creation',
      description: invoice.lines.data[0].price?.id,
      type: 'subscription',
      status: 'confirmed',
      amount: invoice.amount_paid / 100,
      amountIn: invoice.amount_paid / 100,
      amountOut: 0,
      remark: `${invoice.subscription}${invoice.billing_reason === 'subscription_create' && invoice.amount_paid === 0 ? ' (Trial)' : ''}`,
      txnHash: invoice.id,
    })

    // Check if this is a trial invoice
    const isTrial = invoice.billing_reason === 'subscription_create' && invoice.amount_paid === 0

    // Only update status to active if it's a paid invoice
    if (findSubscription.status !== 'active' && !isTrial) {
      findSubscription.status = 'active'
    }

    // If the subscription has an end date, remove it
    if (findSubscription.endDate !== null) {
      findSubscription.endDate = null
    }

    // Save the updated subscription
    await findSubscription.save()


    // update all story order here
    const plan = await Plan.query()
      .where('id', findSubscription.planId)
      .preload('planStories')
      .first()

    if (!plan) {
      console.log('plan.onPurchased', 'plan not found via plan id')

      // NOTE: should not happen
      return
    }

    for (let story of plan.planStories) {
      await StoryOrder.updateOrCreate(
        {
          storyId: story.id,
          userId: user.id,
        },
        {
          storyId: story.id,
          userId: user.id,
          blocked: false,
        }
      )
    }
  }

  private static async onFailed(job) {
    const invoice: stripe.Invoice = job.data
    console.log('plan.onFailed', invoice)

    const user = await User.query()
      .where('stripe_customer_id', invoice.customer as string)
      .orWhere('email', invoice.customer_email ?? 'will-never-trigger')
      .first()

    if (!user) {
      console.log('plan.onFailed', 'user not found via stripe user id')
      throw new Error('User not found')
    }

    const findSubscription = await Subscription.query()
      .where('provider_subscription_id', invoice.subscription as string)
      .first()

    if (!findSubscription) {
      console.log('plan.onFailed', 'subscription not found via subscription id')
      throw new Error('Subscription not found')
    }

    findSubscription.status = 'canceled'
    findSubscription.endDate = DateTime.now()
    await findSubscription.save()

    // Block access to stories if trial expired or payment failed
    const plan = await Plan.query()
      .where('id', findSubscription.planId)
      .preload('planStories')
      .first()

    if (!plan) {
      // NOTE: should not happen
      return
    }

    for (let story of plan.planStories) {
      await StoryOrder.query()
        .where('user_id', findSubscription.userId)
        .where('story_id', story.id)
        .update({ blocked: true })
    }
  }
}
