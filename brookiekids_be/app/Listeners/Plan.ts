import stripe from 'stripe'
import { PlanJobKeys, planQueue } from './PlanQueue'

export default class PlanListener {
  public async onStripeProductCreated(product: stripe.Product) {
    console.log('PlanListener.onStripeProductCreated', product)
    await planQueue.add(PlanJobKeys.PRODUCT_CREATED, product)
  }

  public async onStripePriceCreated(price: stripe.Price) {
    console.log('PlanListener.onStripePriceCreated', price)
    await planQueue.add(PlanJobKeys.PRICE_CREATED, price)
  }

  public async onStripeProductUpdated(product: stripe.Product) {
    console.log('PlanListener.onStripeProductUpdated', product)
    await planQueue.add(PlanJobKeys.PRODUCT_UPDATED, product)
  }

  public async onStripePriceUpdated(price: stripe.Price) {
    console.log('PlanListener.onStripePriceUpdated', price)
    await planQueue.add(PlanJobKeys.PRICE_UPDATED, price)
  }

  public async onStripeProductDeleted(product: stripe.Product) {
    console.log('PlanListener.onStripeProductDeleted', product)
    await planQueue.add(PlanJobKeys.PRODUCT_DELETED, product)
  }

  public async onStripePriceDeleted(price: stripe.Price) {
    console.log('PlanListener.onStripePriceDeleted', price)
    await planQueue.add(PlanJobKeys.PRICE_DELETED, price)
  }

  public async onCustomerCreated(customer: stripe.Customer) {
    console.log('PlanListener.onCustomerCreated', customer)
    await planQueue.add(PlanJobKeys.CUSTOMER_CREATED, customer)
  }

  public async onCustomerUpdated(customer: stripe.Customer) {
    console.log('PlanListener.onCustomerUpdated', customer)
    await planQueue.add(PlanJobKeys.CUSTOMER_UPDATED, customer)
  }

  public async onSubscriptionCreated(subscription: stripe.Subscription) {
    console.log('PlanListener.onSubscriptionCreated', subscription)
    await planQueue.add(PlanJobKeys.SUBSCRIPTION_CREATED, subscription)
  }

  public async onSubscriptionUpdated(subscription: stripe.Subscription) {
    console.log('PlanListener.onSubscriptionUpdated', subscription)
    await planQueue.add(PlanJobKeys.SUBSCRIPTION_UPDATED, subscription)
  }

  public async onSubscriptionDeleted(subscription: stripe.Subscription) {
    console.log('PlanListener.onSubscriptionDeleted', subscription)
    await planQueue.add(PlanJobKeys.SUBSCRIPTION_DELETED, subscription)
  }

  public async onPurchased(invoice: stripe.Invoice) {
    console.log('PlanListener.onPurchased', invoice)
    await planQueue.add(PlanJobKeys.INVOICE_PAID, invoice)
  }

  public async onPaymentFailed(invoice: stripe.Invoice) {
    console.log('PlanListener.onPaymentFailed', invoice)
    await planQueue.add(PlanJobKeys.INVOICE_FAILED, invoice)
  }

  // public async onStripeProductCreated(product: stripe.Product) {
  //   console.log('plan.onStripeProductCreated', product)

  //   const plan = await Plan.query().where('stripe_product_id', product.id).first()
  //   // plan pricing should be null
  //   if (plan) {
  //     return
  //   }

  //   await Plan.create({ title: product.name, stripeProductId: product.id })
  // }

  // public async onStripePriceCreated(price: stripe.Price) {
  //   console.log('plan.onStripePriceCreated', price)

  //   const plan = await Plan.findByOrFail('stripe_product_id', price.product as string)

  //   await PlanPricing.updateOrCreate(
  //     { stripePriceId: price.id, planId: plan.id },
  //     {
  //       stripePriceId: price.id,
  //       planId: plan.id,
  //       price: (price.unit_amount ?? 0) / 100,
  //       duration:
  //         price.recurring?.interval === 'month'
  //           ? 'month'
  //           : price.recurring?.interval === 'year'
  //           ? 'year'
  //           : undefined,
  //     }
  //   )
  // }

  // public async onStripeProductUpdated(product: stripe.Product) {
  //   console.log('plan.onStripeProductUpdated', product)

  //   const plan = await Plan.query().where('stripe_product_id', product.id).first()
  //   // plan should not be null
  //   if (!plan) {
  //     return
  //   }

  //   plan.title = product.name

  //   await plan.save()
  // }

  // public async onStripePriceUpdated(price: stripe.Price) {
  //   console.log('plan.onStripePriceUpdated', price)
  //   const plan = await Plan.findByOrFail('stripe_product_id', price.product as string)

  //   await PlanPricing.updateOrCreate(
  //     { stripePriceId: price.id, planId: plan.id },
  //     {
  //       stripePriceId: price.id,
  //       planId: plan.id,
  //       price: (price.unit_amount ?? 0) / 100,
  //       duration:
  //         price.recurring?.interval === 'month'
  //           ? 'month'
  //           : price.recurring?.interval === 'year'
  //           ? 'year'
  //           : undefined,
  //       blocked: false,
  //     }
  //   )
  // }

  // public async onStripeProductDeleted(product: stripe.Product) {
  //   console.log('plan.onStripeProductDeleted', product)
  //   const plan = await Plan.findByOrFail('stripe_product_id', product.id)
  //   plan.blocked = true
  //   await plan.save()

  //   // const planPricing = await PlanPricing.query().where('stripeProductId', product.id).first()

  //   // // planPricing should not be null
  //   // if (!planPricing) {
  //   //   console.log('planPricing.onStripeProductDeleted', 'unable to find product')
  //   //   return
  //   // }

  //   // // planPricing.stripeProductId = null
  //   // // block this because stripe product is invalid
  //   // planPricing.blocked = true
  //   // await planPricing.save()
  // }

  // public async onStripePriceDeleted(price: stripe.Price) {
  //   console.log('plan.onStripePriceDeleted', price)

  //   const planPricing = await PlanPricing.query().where('stripe_price_id', price.id).first()

  //   // planPricing should not be null
  //   if (!planPricing) {
  //     return
  //   }

  //   planPricing.blocked = true
  //   await planPricing.save()
  // }

  // public async onCustomerCreated(customer: stripe.Customer) {
  //   console.log('plan.onCustomerCreated', customer)

  //   if (customer.email === null) {
  //     return
  //   }

  //   let user = await User.query().where('email', customer.email!).first()

  //   if (!user) {
  //     // email not exist, need to create user
  //     // preferably not
  //     user = await User.create({
  //       stripeCustomerId: customer.id,
  //       email: customer.email!,
  //       name: customer.name ?? undefined,
  //     })
  //   }

  //   // link stripe customer to user
  //   user.stripeCustomerId = customer.id
  //   await user.save()
  // }

  // public async onSubscriptionCreated(subscription: stripe.Subscription) {
  //   console.log('plan.onSubscriptionCreated', subscription)

  //   let user = await User.query()
  //     .where('stripe_customer_id', subscription.customer as string)
  //     .first()

  //   if (!user) {
  //     // TODO
  //     throw new Error('User not found')
  //   }

  //   const findPlanPricing = await PlanPricing.query()
  //     .where('stripe_price_id', subscription.items.data[0].price.id)
  //     .preload('plan')
  //     .first()

  //   if (!findPlanPricing) {
  //     throw new Error('Plan not found')
  //   }

  //   // create subscription
  //   await Subscription.create({
  //     userId: user.id,
  //     planId: findPlanPricing.planId,
  //     planPricingId: findPlanPricing.id,
  //     provider: 'stripe',
  //     providerUserId: subscription.customer as string,
  //     providerSubscriptionId: subscription.id,
  //     providerPlanId: findPlanPricing.stripePriceId as string,
  //     status: subscription.status,
  //     startDate: DateTime.fromSeconds(subscription.start_date),
  //     cycleStartDate: DateTime.fromSeconds(subscription.current_period_start),
  //     cycleEndDate: DateTime.fromSeconds(subscription.current_period_end),
  //   })

  //   // create all story order here?
  // }

  // public async onSubscriptionUpdated(subscription: stripe.Subscription) {
  //   console.log('plan.onSubscriptionUpdated', subscription)

  //   let user = await User.query()
  //     .where('stripe_customer_id', subscription.customer as string)
  //     .first()

  //   if (!user) {
  //     // TODO
  //     throw new Error('User not found')
  //   }

  //   // find subscription
  //   const findSubscription = await Subscription.query()
  //     .where('provider_subscription_id', subscription.id)
  //     .first()

  //   if (!findSubscription) {
  //     throw new Error('Subscription not found')
  //   }

  //   // update subscription
  //   findSubscription.merge({
  //     // userId: user.id,
  //     // planId: findPlan.id,
  //     // provider: 'stripe',
  //     // providerUserId: subscription.customer as string,
  //     // providerSubscriptionId: subscription.id,
  //     // providerPlanId: findPlan.stripePriceId,
  //     status: subscription.status,
  //     startDate: DateTime.fromSeconds(subscription.start_date),
  //     cycleStartDate: DateTime.fromSeconds(subscription.current_period_start),
  //     cycleEndDate: DateTime.fromSeconds(subscription.current_period_end),
  //   })
  //   await findSubscription.save()
  // }

  // public async onPurchased(invoice: stripe.Invoice) {
  //   console.log('plan.onPurchased', invoice)

  //   const user = await User.query()
  //     .where('stripe_customer_id', invoice.customer as string)
  //     .orWhere('email', invoice.customer_email ?? 'will-never-trigger')
  //     .first()

  //   if (!user) {
  //     // TODO
  //     throw new Error('User not found')
  //   }

  //   const findSubscription = await Subscription.query()
  //     .where('provider_subscription_id', invoice.subscription as string)
  //     .first()

  //   if (!findSubscription) {
  //     throw new Error('Subscription not found')
  //   }

  //   await Transaction.create({
  //     userId: user.id,
  //     title: 'Subscription creation',
  //     description: invoice.lines.data[0].price?.id, // price id
  //     type: 'subscription',
  //     status: 'confirmed',
  //     amount: invoice.amount_paid / 100, // in cents
  //     amountIn: invoice.amount_paid / 100, // in cents
  //     amountOut: 0,
  //     remark: `${invoice.subscription}`, // subscription id
  //     txnHash: invoice.id, // invoice id
  //   })
  // }
}
