import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, beforeCreate, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import User from './User'
import Wallet from './Wallet'
import CryptoJS from 'crypto-js'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import TransactionFilter from './Filters/TransactionFilter'

export const randomNumber = (min: number, max: number) => {
  if (min > max) {
    throw new Error('min cannot be smaller than max')
  }
  return Math.floor(Math.random() * (max - min) + min)
}

export default class Transaction extends compose(BaseModel, Filterable) {
  public static $filter = () => TransactionFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public walletId: number

  @column()
  public userId: number

  @column()
  public title: string

  @column()
  public description: string

  @column()
  public type: string

  @column()
  public status: string

  @column({ consume: (value: number) => Number(value) })
  public amount: number

  @column({ consume: (value: number) => Number(value) })
  public amountIn: number

  @column({ consume: (value: number) => Number(value) })
  public amountOut: number

  @column()
  public remark: string

  @column()
  public txnHash: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => User)
  public user: BelongsTo<typeof User>

  @belongsTo(() => Wallet)
  public wallet: BelongsTo<typeof Wallet>

  @beforeCreate()
  public static async updateField(txn: Transaction) {
    if (txn.$dirty.createdAt === null) {
      txn.createdAt = DateTime.local()
    }

    if (txn.$dirty.txnHash === null) {
      txn.txnHash = CryptoJS.SHA256(
        `${txn.walletId}${txn.userId}${txn.title}${txn.description}${txn.amount}${txn.createdAt
          .startOf('second')
          .toISO({ suppressMilliseconds: true })}${randomNumber(100000, 999999)}`
      ).toString()
    }
  }
}
