import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Subscription from 'App/Models/Subscription'
import Plan from 'App/Models/Plan'
import StoryOrder from 'App/Models/StoryOrder'
import { DateTime } from 'luxon'
import User from 'App/Models/User'

export default class extends BaseSeeder {
  public async run() {
    const users = ['<EMAIL>']
    const userList = await User.query().whereIn('email', users)

    for (const user of userList) {
      const subscription = await Subscription.query()
        .where('user_id', user.id)
        .orderBy('id', 'desc')
        .firstOrFail()

      // if (subscription.status !== 'active') {
      // update subscription
      subscription.merge({
        status: 'active',
        endDate: null,
        cycleEndDate: DateTime.fromFormat('2026-05-11', 'yyyy-MM-dd'),
      })
      await subscription.save()
      // }

      // find all story order & activate it
      const plan = await Plan.query()
        .where('id', subscription.planId)
        .preload('planStories')
        .first()

      if (!plan) {
        // NOTE: should not happen
        return
      }

      for (let story of plan.planStories) {
        await StoryOrder.updateOrCreate(
          {
            storyId: story.id,
            userId: subscription.userId,
          },
          {
            storyId: story.id,
            userId: subscription.userId,
            blocked: false,
          }
        )
      }
    }
  }
}
