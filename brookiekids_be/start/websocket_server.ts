/*
|--------------------------------------------------------------------------
| Preloaded File
|--------------------------------------------------------------------------
|
| Any code written inside this file will be executed during the application
| boot.
|
*/
import { createServer } from 'http'
import {
  ChatWs,
  listenToChats,
  stopListenToChats,
  listenToHistories,
  stopListenToHistories,
  arrayBufferToString,
  stringToBuffer,
} from 'App/Network/ws_service'
import { v4 as uuid } from 'uuid'
import { WebSocket } from 'ws'
import { parse } from 'url'
import _ from 'lodash'
import Database from '@ioc:Adonis/Lucid/Database'
import { DateTime } from 'luxon'

type Message = {
  action: 'subscribe' | 'unsubscribe' | 'ping'
  id?: number
}

// boot the web socket server
ChatWs.boot()
const wsServer = createServer({})

const authenticate = async (headers, callback: (err?: any) => any) => {
  const token = _.trim(headers['authorization']?.replace(/bearer\s+/gi, ''))
  if (token) {
    try {
      const exist = await Database.query()
        .select('*')
        .from('api_tokens')
        .where('token', token)
        .andWhere('expires_at', '>', DateTime.now().toSQL())
      if (exist.length === 0 || !exist) {
        callback({ error: 'Not authenticated' })
        return
      }
      callback()
      return
    } catch (e) {
      callback(e)
      return
    }
  }
  callback({ error: 'Not authenticated' })
}

wsServer.on('upgrade', (request, socket, head) => {
  socket.on('error', (e) => {
    console.log('ws upgrade error', e)
  })

  // This function is not defined on purpose. Implement it with your own logic.
  authenticate(request.headers, (err) => {
    if (err) {
      socket.write('HTTP/1.1 401 Unauthorized\r\n\r\n')
      socket.destroy()
      return
    }

    socket.removeListener('error', (e) => console.log('ws upgrade error', e))

    ChatWs.io.handleUpgrade(request, socket, head, (ws) => {
      ChatWs.io.emit('connection', ws, request)
    })
  })
})

ChatWs.io.on('connection', async (socket: WebSocket, request) => {
  // console.log('connected to feedback ws')
  // console.log(request.headers['authorization'])
  socket.on('message', async (data: ArrayBuffer) => {
    try {
      const { pathname } = parse(request.url ?? '')
      const obj: Message = JSON.parse(arrayBufferToString(data))

      switch (pathname) {
        case '/':
          switch (obj.action) {
            case 'subscribe':
              const token = uuid()
              await listenToChats(token, socket, request)
              break
            case 'unsubscribe':
              stopListenToChats(socket['connId'])
              break
            case 'ping':
              socket.send(stringToBuffer(JSON.stringify({ pong: 'pong' })))
              break
          }
          break

        case '/histories':
          switch (obj.action) {
            case 'subscribe':
              const token = uuid()
              await listenToHistories(obj.id!, token, socket, request)
              break
            case 'unsubscribe':
              stopListenToHistories(socket['connId'])
              break
            case 'ping':
              socket.send(stringToBuffer(JSON.stringify({ pong: 'pong' })))
              break
          }
          break
      }
    } catch (e) {
      console.log('websocket error ', e)
    }
  })

  socket.on('close', () => {
    const { pathname } = parse(request.url ?? '')
    const connId = socket['connId']

    switch (pathname) {
      case '/':
        stopListenToChats(connId)
        break

      case '/histories':
        stopListenToHistories(connId)
        break
    }
  })
})

// wsServer.listen(parseInt(process.env.WS_PORT!))
