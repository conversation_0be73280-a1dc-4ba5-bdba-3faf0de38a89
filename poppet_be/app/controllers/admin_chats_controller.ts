import type { HttpContext } from '@adonisjs/core/http'
import Chat from '../models/chat.js'
import ChatHistory from '../models/chat_history.js'
import User from '#models/user'
import emitter from '@adonisjs/core/services/emitter'
import { createAdminChatValidator, createChatHistoryValidator } from '#validators/chat_validator'

export default class AdminChatsController {
  /**
   * Get paginated list of all chats (admin view)
   */
  async find({ response, request }: HttpContext) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'latest_message_date:desc').split(':')

    const result = await Chat.query()
      .preload('user')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.ok(result)
  }

  /**
   * Get a specific chat (admin view)
   */
  async findOne({ response, params }: HttpContext) {
    const result = await Chat.query().where('id', params.id).preload('user').first()

    if (!result) {
      return response.notFound()
    }

    return response.ok(result)
  }

  /**
   * Create a new chat as admin
   */
  async create({ response, request, auth }: HttpContext) {
    try {
      const payload = await request.validateUsing(createAdminChatValidator)

      const user = await User.query()
        .where('email', payload.email)
        .whereNot((query) => query.has('admin'))
        .first()

      if (!user) {
        return response.badRequest({ success: false, message: 'Invalid user' })
      }

      const chat = await Chat.create({ userId: user.id })
      const history = new ChatHistory()

      history.merge({
        userId: auth.user?.id,
        content: payload.content,
        chatId: chat.id,
        createdAt: chat.createdAt,
      })
      await history.save()

      emitter.emit('chat:create', chat.id)
      return response.created({ data: chat, success: true })
    } catch (e) {
      console.log(e)
      return response.badRequest(e)
    }
  }

  /**
   * Create a new chat history entry as admin
   */
  async createHistory({ request, response, params, auth }: HttpContext) {
    try {
      const chat = await Chat.query().where('id', params.id).first()
      if (!chat) {
        return response.notFound()
      }

      const payload = await request.validateUsing(createChatHistoryValidator)

      const history = await ChatHistory.create({
        ...payload,
        userId: auth.user!.id,
        chatId: chat.id,
      })

      emitter.emit('history:create', history.id)
      return response.created({ data: history, success: true })
    } catch (e) {
      return response.badRequest(e)
    }
  }

  /**
   * Get paginated chat histories for a specific chat (admin view)
   */
  async findHistories({ request, response, params }: HttpContext) {
    const chat = await Chat.query().where('id', params.id).first()
    if (!chat) {
      return response.notFound()
    }

    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'latest_message_date:desc').split(':')

    const result = await ChatHistory.query()
      .where('chat_id', params.id)
      .preload('user')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.ok(result)
  }
}
