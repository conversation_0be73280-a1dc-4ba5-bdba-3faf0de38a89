import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import db from '@adonisjs/lucid/services/db'
import { DateTime } from 'luxon'

import Auth from '#models/auth'
import User from '#models/user'
import Child from '#models/child'
import UserEvent from '#models/user_event'
import Wallet from '#models/wallet'

import {
  registerValidator,
  loginValidator,
  loginMagicValidator,
  forgotPasswordValidator,
  changePasswordValidator,
  sendAuthCodeValidator,
  verifyAuthCodeValidator,
} from '#validators/auth_validator'

interface ApiToken {
  user: { id: number }
  name: string
  value?: { release(): string }
}

const removeDuplicateApiToken = async (token: ApiToken) => {
  try {
    // kick user from multiple login
    const lastToken = await db
      .from('api_tokens')
      .where('user_id', token.user.id)
      .orderBy('id', 'desc')
      .where('name', token.name)
      .first()
    await db
      .from('api_tokens')
      .where('user_id', token.user.id)
      .whereNot('id', lastToken.id)
      .where('name', token.name)
      .delete()

    return true
  } catch (error) {
    logger.debug('Delete token issue : %o', error)
    return false
  }
}

const subscribeToMailerLite = ({ email }: { email: string; region: string }) => {
  console.log('Subscribing to MailerLite:', email)
  // Implementation for MailerLite subscription
}

const logUserEvent = async (
  userId: number,
  type: string,
  request: HttpContext['request'],
  ipAddress: string,
  location: string,
  provider: string
) => {
  await UserEvent.create({
    type: type,
    device: request.header('user-agent') || 'Unknown',
    ipAddress: ipAddress,
    location: location,
    userId: userId,
    provider: provider,
  })
}

export default class AuthController {
  public async register({ request, response }: HttpContext) {
    const validationData = await request.validateUsing(registerValidator)

    const { ...input } = validationData

    const now = DateTime.local().toISO()
    const ago = DateTime.local().plus({ minutes: -15 }).toISO()

    // can register with either email or phone
    if (validationData.email !== null) {
      const checkEmailRequest = await Auth.query()
        .where('email', validationData.email)
        .where('code', validationData.auth_code)
        .where('created_at', '>', ago)
        .where('created_at', '<', now)
        .where('status', 1)
        .first()

      if (checkEmailRequest === null) {
        return response.badRequest({
          message: 'Code is invalid or expired, please verify your email first',
          code: 'code.invalid',
          success: false,
        })
      }
    } else if (validationData.phone) {
      // TODO: check if phone is duplicated
    }

    try {
      const user = await User.create({
        ...input,
        verified: true,
        region: 'sg', // default region
      })

      // Create wallet for user
      await Wallet.create({
        userId: user.id,
      })

      // Subscribe to MailerLite if email provided
      if (validationData.email) {
        subscribeToMailerLite({
          email: validationData.email,
          region: user.region || 'sg',
        })
      }

      // Log user event
      await logUserEvent(
        user.id,
        'email',
        request,
        validationData.ip_address || 'Unknown',
        validationData.location || 'Unknown',
        'register'
      )

      return response.created({
        success: true,
        data: user,
      })
    } catch (error) {
      logger.error('Registration error: %o', error)
      return response.badRequest({
        success: false,
        message: 'Registration failed',
      })
    }
  }

  public async login({ request, auth, response }: HttpContext) {
    const validationData = await request.validateUsing(loginValidator)
    const { email, username, password } = validationData

    try {
      let identifier = ''
      if (email !== null) {
        identifier = email
      } else if (username !== null) {
        identifier = username
      }

      const verifiedUser = await User.verifyCredentials(identifier, password)
      const token = await auth.use('api').createToken(verifiedUser, {
        expiresIn: '3days',
        name: 'Opaque Access Token',
      })

      await removeDuplicateApiToken(token)

      const user = await User.query().where('id', token.user.id).firstOrFail()
      await user.save()

      await logUserEvent(
        user.id,
        'email',
        request,
        validationData?.ip_address ?? 'Unknown',
        validationData?.location ?? 'Unknown',
        'login'
      )

      return response.ok({
        success: true,
        data: {
          user,
          token: token.value?.release(),
        },
      })
    } catch (error) {
      logger.error('Login error: %o', error)
      return response.badRequest({
        success: false,
        message: 'Invalid credentials',
      })
    }
  }

  public async sendAuthCode({ request, response }: HttpContext) {
    const validationData = await request.validateUsing(sendAuthCodeValidator)

    const email = validationData.email
    const now = DateTime.local().toISO()
    const ago = DateTime.local().plus({ seconds: 15 }).toISO()
    const code = (Math.floor(Math.random() * 10000) + 10000).toString().substring(1)

    console.log(email, code)
    const checkEmailRequest = await Auth.query()
      .where('email', email)
      .whereBetween('created_at', [ago, now])
      .where('status', 0)
      .first()

    if (checkEmailRequest !== null) {
      return response.badRequest({
        message: 'Please wait 15 seconds before requesting another code',
        code: 'auth.email.code.wait',
        success: false,
        data: {},
      })
    }

    const expiredAt = DateTime.local().plus({ minutes: 15 }).toISO()

    try {
      await Auth.create({
        email: email,
        code: code,
        expiredAt: expiredAt,
        status: 0,
      })

      // TODO: Send email with code
      // await Event.emit('auth:sendCode', { email, code })

      return response.ok({
        success: true,
        message: 'Verification code sent to your email',
      })
    } catch (error) {
      logger.error('Send auth code error: %o', error)
      return response.badRequest({
        success: false,
        message: 'Failed to send verification code',
      })
    }
  }

  public async verifyAuthCode({ request, response }: HttpContext) {
    const validationData = await request.validateUsing(verifyAuthCodeValidator)
    const input = {
      email: validationData.email,
      code: validationData.code,
    }

    const now = DateTime.local().toSQL()

    const checkEmailRequest = await Auth.query()
      .where('email', input.email)
      .where('code', input.code)
      .where('expired_at', '>', now)
      .where('status', 0)
      .first()

    if (checkEmailRequest === null) {
      return response.badRequest({
        message: 'Code is invalid or expired',
        code: 'auth.email.code.invalid',
        success: false,
        data: {},
      })
    }

    await Auth.query().where('id', checkEmailRequest?.id).update({
      status: 1,
    })

    return response.ok({
      success: true,
    })
  }

  public async forgotPassword({ request, response }: HttpContext) {
    const validationData = await request.validateUsing(forgotPasswordValidator)

    const input = {
      email: validationData.email,
      password: validationData.password,
    }

    const now = DateTime.local().toSQL()

    const checkEmailRequest = await Auth.query()
      .where('email', input.email)
      .where('code', validationData.auth_code)
      .where('expired_at', '>', now)
      .where('status', 1)
      .first()

    if (checkEmailRequest === null) {
      return response.badRequest({
        message: 'Code is invalid or expired',
        code: 'auth.email.code.invalid',
        success: false,
        data: {},
      })
    }

    const user = await User.query().where('email', input.email).first()
    if (user === null) {
      return response.badRequest({
        message: 'User not found',
        success: false,
      })
    }

    user.password = input.password
    await user.save()

    await Auth.query().where('id', checkEmailRequest?.id).update({
      status: 2,
    })

    return response.ok({
      success: true,
      message: 'Password updated successfully',
    })
  }

  public async changePassword({ request, response, auth }: HttpContext) {
    const user = await User.findOrFail(auth?.user?.id)
    const validationData = await request.validateUsing(changePasswordValidator)

    if (!(await User.verifyPassword(validationData.password, user.password))) {
      return response.badRequest({
        code: 'password.change.failed.password.not.matched',
        message: 'Password not match',
      })
    }

    user.password = validationData.new_password
    await user.save()

    return response.ok({
      success: true,
      message: 'Password changed successfully',
    })
  }

  public async loginMagic({ auth, request, response }: HttpContext) {
    const validationData = await request.validateUsing(loginMagicValidator)

    const checkEmailRequest = await Auth.query()
      .where('email', validationData.email)
      .where('code', validationData.code)
      .where('status', 0)
      .first()

    if (checkEmailRequest === null) {
      return response.badRequest({
        message: 'Code is invalid or expired',
        code: 'auth.email.code.invalid',
        success: false,
        data: {},
      })
    }

    let user = await User.query().where('email', validationData.email).first()

    if (user === null) {
      // Create new user if doesn't exist
      user = await User.create({
        email: validationData.email,
        name: validationData.name || '',
        region: validationData.region || 'sg',
        verified: true,
      })

      // Create wallet for new user
      await Wallet.create({
        userId: user.id,
      })

      // Create child if provided
      if (validationData.child_name && validationData.child_dob) {
        const child = await Child.create({
          name: validationData.child_name,
          birthdate: validationData.child_dob,
          userId: user.id,
        })
        user.activeChildId = child.id
        await user.save()
      }

      subscribeToMailerLite({
        email: validationData.email,
        region: user.region || 'sg',
      })
    }

    const token = await auth.use('api').createToken(user, {
      expiresIn: '3days',
      name: 'Opaque Access Token',
    })

    await removeDuplicateApiToken(token)

    await Auth.query().where('id', checkEmailRequest?.id).update({
      status: 1,
    })

    await logUserEvent(user.id, 'email', request, 'Unknown', 'Unknown', 'magic_login')

    return response.ok({
      success: true,
      data: {
        user,
        token: token.value?.release(),
      },
    })
  }

  public async logout({ auth, response }: HttpContext) {
    try {
      await auth.use('api').invalidateToken()
      return response.ok({
        success: true,
        message: 'Logged out successfully',
      })
    } catch (error) {
      logger.error('Logout error: %o', error)
      return response.badRequest({
        success: false,
        message: 'Logout failed',
      })
    }
  }

  public async me({ auth, response }: HttpContext) {
    try {
      const user = await auth.authenticate()
      await user.load('children')
      await user.load('wallet')

      return response.ok({
        success: true,
        data: user,
      })
    } catch {
      return response.unauthorized({
        success: false,
        message: 'Unauthorized',
      })
    }
  }
}
