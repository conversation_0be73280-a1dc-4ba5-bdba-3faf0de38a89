import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import { omit, keys } from 'radash'

import Banner from '#models/banner'
import { createBannerValidator, updateBannerValidator } from '#validators/banner_validator'

export default class BannersController {
  /**
   * Get banners with filtering and pagination (public endpoint)
   */
  public async find({ request, response }: HttpContext) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'id:desc').split(':')
      const filters = omit(request.all(), ['page', 'limit', 'sort'])

      // Default to Singapore region if not specified
      if (!keys(filters).includes('region')) {
        filters['region'] = 'sg'
      }

      const banners = await Banner.filter(filters).orderBy(sort[0], sort[1]).paginate(page, limit)

      return response.status(200).send(banners)
    } catch (error) {
      logger.error('Banners find error: %o', error)
      return response.status(400).send(error)
    }
  }

  /**
   * Get banners with filtering and pagination (admin endpoint)
   */
  public async adminFind({ request, response }: HttpContext) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'id:desc').split(':')
      const filters = omit(request.all(), ['page', 'limit', 'sort'])

      const banners = await Banner.filter(filters).orderBy(sort[0], sort[1]).paginate(page, limit)

      return response.status(200).send(banners)
    } catch (error) {
      logger.error('Banners adminFind error: %o', error)
      return response.status(400).send(error)
    }
  }

  /**
   * Get a single banner by ID
   */
  public async findOne({ response, params }: HttpContext) {
    try {
      const banner = await Banner.query().where('id', params.id).first()

      return response.status(200).send({ data: banner })
    } catch (error) {
      logger.error('Banner findOne error: %o', error)
      return response.status(400).send(error)
    }
  }

  /**
   * Create a new banner
   */
  public async create({ request, response }: HttpContext) {
    try {
      const validationData = await request.validateUsing(createBannerValidator)

      const createBanner = await Banner.create({
        thumbnailUrl: validationData.thumbnail_url,
        ctaUrl: validationData.cta_url ?? '',
        region: validationData.region,
      })

      return response.status(200).send({ success: true, data: createBanner })
    } catch (error) {
      logger.error('Banner create error: %o', error)
      return response.status(400).send(error)
    }
  }

  /**
   * Update a banner by ID
   */
  public async update({ request, response, params }: HttpContext) {
    try {
      const validationData = await request.validateUsing(updateBannerValidator)

      const updateBanner = await Banner.findOrFail(params.id)
      updateBanner.merge({
        thumbnailUrl: validationData.thumbnail_url,
        ctaUrl: validationData.cta_url ?? '',
        region: validationData.region,
      })
      await updateBanner.save()

      return response.status(200).send({ success: true, data: updateBanner })
    } catch (error) {
      logger.error('Banner update error: %o', error)
      return response.status(400).send(error)
    }
  }

  /**
   * Delete a banner by ID
   */
  public async delete({ response, params }: HttpContext) {
    try {
      const deleteBanner = await Banner.query().where('id', params.id).delete()

      return response.status(200).send({
        success: true,
        data: deleteBanner,
      })
    } catch (error) {
      logger.error('Banner delete error: %o', error)
      return response.status(400).send(error)
    }
  }
}
