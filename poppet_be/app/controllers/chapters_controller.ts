import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import db from '@adonisjs/lucid/services/db'
import { omit } from 'radash'

import Chapter from '#models/chapter'
import Story from '#models/story'
import User from '#models/user'
import File from '#models/file'
import { createChapterValidator, updateChapterValidator } from '#validators/chapter_validator'
import { uploadToS3Bucket, createMediaConvertJob } from './files_controller.js'

export default class ChaptersController {
  /**
   * Get all chapters with filtering and pagination
   */
  public async findAll({ request, response }: HttpContext) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'id:desc').split(':')
      const filters = omit(request.all(), ['page', 'sort', 'limit'])

      const allChapters = await Chapter.filter(filters)
        .preload('video')
        .preload('story')
        .preload('fallbackChapter')
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.status(200).send(allChapters)
    } catch (error) {
      logger.error('Chapters findAll error: %o', error)
      return response.status(400).send(error)
    }
  }

  /**
   * Get chapters by pack and level
   */
  public async findChaptersByPackAndLevel({ params, request, response }: HttpContext) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'id:desc').split(':')
      const filters = omit(request.all(), ['page', 'sort', 'limit'])

      const allChapters = await Chapter.filter(filters)
        .whereHas('story', (query) => {
          query.where('pack_id', params.pack_id).where('level', params.level)
        })
        .preload('story', (query) => query.preload('chapters'))
        .preload('fallbackChapter')
        .preload('video')
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.status(200).send(allChapters)
    } catch (error) {
      logger.error('Chapters findChaptersByPackAndLevel error: %o', error)
      return response.status(400).send(error)
    }
  }

  /**
   * Get chapters by story ID
   */
  public async findChaptersByStoryId({ params, request, response }: HttpContext) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'id:desc').split(':')
      const filters = omit(request.all(), ['page', 'sort', 'limit'])

      const chapters = await Chapter.filter(filters)
        .where('story_id', params.story_id)
        .preload('story')
        .preload('video')
        .preload('fallbackChapter')
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.status(200).send(chapters)
    } catch (error) {
      logger.error('Chapters findChaptersByStoryId error: %o', error)
      return response.status(400).send(error)
    }
  }

  /**
   * Get a single chapter by ID
   */
  public async findOne({ params, response }: HttpContext) {
    try {
      const chapter = await Chapter.query().where('id', params.id).firstOrFail()

      return response.status(200).send({ data: chapter })
    } catch (error) {
      logger.error('Chapter findOne error: %o', error)
      return response.status(400).send(error)
    }
  }

  /**
   * Create a new chapter
   */
  public async create({ request, response, auth }: HttpContext) {
    try {
      const user = await User.findOrFail(auth?.user?.id)
      const validationData = await request.validateUsing(createChapterValidator)

      let videoSrc = ''
      if (validationData.video_file) {
        const uploadBucket = await uploadToS3Bucket(
          validationData.video_file,
          process.env.S3_BUCKET_VIDEO ?? 'hummusedu',
          user.email
        )

        if (!uploadBucket?.url) {
          return response.status(400).send({
            success: false,
            message: 'Failed to upload video',
          })
        }

        const inputS3Url = `s3://${process.env.S3_BUCKET_VIDEO}/${uploadBucket.key}`
        const outputS3Url = `s3://${process.env.S3_BUCKET_HLS}/`
        const job = await createMediaConvertJob(inputS3Url, outputS3Url)

        if (job.Status !== 'SUBMITTED') {
          return response.status(400).send({
            success: false,
            message: 'Failed to process video',
          })
        }

        videoSrc = `${process.env.S3_CLOUDFRONT_VIDEO}${uploadBucket.key.replace(
          '.mp4',
          '_1080p.m3u8'
        )}`
      }

      let audioSrc: string = ''
      if (validationData.audio_file) {
        const uploadBucket = await uploadToS3Bucket(
          validationData.audio_file,
          process.env.S3_BUCKET ?? 'hummusedu',
          user.email
        )

        if (!uploadBucket?.url) {
          return response.status(400).send({
            success: false,
            message: 'Failed to upload audio',
          })
        }

        audioSrc = uploadBucket.url
      }

      const findStory = await Story.findOrFail(validationData.story_id)
      const result = await db.transaction(async (trx) => {
        let fileId: number | undefined
        if (videoSrc) {
          const chapterVideo = new File()
          chapterVideo.src = videoSrc
          chapterVideo.type = 'video'
          chapterVideo.useTransaction(trx)
          await chapterVideo.save()
          fileId = chapterVideo.id
        }

        const newChapter = new Chapter()
        newChapter.title = validationData.title
        if (validationData.description) {
          newChapter.description = validationData.description
        }
        newChapter.storyId = validationData.story_id
        if (fileId) {
          newChapter.fileId = fileId
        }
        if (validationData.fallback_chapter_id) {
          newChapter.fallbackChapterId = validationData.fallback_chapter_id
        }
        newChapter.isFreeResponse = validationData.is_free_response

        if (validationData.is_free_response) {
          newChapter.options = [{ value: 'free response' }]
        } else {
          newChapter.options = validationData.options ? validationData.options : null
        }

        if (audioSrc) {
          newChapter.audioUrl = audioSrc
        }

        newChapter.useTransaction(trx)
        await newChapter.save()

        if (findStory.defaultChapterId === null) {
          findStory.defaultChapterId = newChapter.id
          findStory.useTransaction(trx)
          await findStory.save()
        }

        return newChapter
      })

      return response.status(200).send({ success: true, data: result })
    } catch (error) {
      logger.error('Chapter create error: %o', error)
      return response.status(400).send(error)
    }
  }

  /**
   * Update a chapter by ID
   */
  public async update({ request, response, params, auth }: HttpContext) {
    try {
      const user = await User.findOrFail(auth?.user?.id)
      const validationData = await request.validateUsing(updateChapterValidator)

      const updateChapter = await Chapter.findOrFail(params.id)
      let videoSrc = ''
      if (validationData.video_file) {
        const uploadBucket = await uploadToS3Bucket(
          validationData.video_file,
          process.env.S3_BUCKET_VIDEO ?? 'hummusedu',
          user.email
        )

        if (!uploadBucket?.url) {
          return response.status(400).send({
            success: false,
            message: 'Failed to upload video',
          })
        }

        const inputS3Url = `s3://${process.env.S3_BUCKET_VIDEO}/${uploadBucket.key}`
        const outputS3Url = `s3://${process.env.S3_BUCKET_HLS}/`
        const job = await createMediaConvertJob(inputS3Url, outputS3Url)

        if (job.Status !== 'SUBMITTED') {
          return response.status(400).send({
            success: false,
            message: 'Failed to process video',
          })
        }

        videoSrc = `${process.env.S3_CLOUDFRONT_VIDEO}${uploadBucket.key.replace(
          '.mp4',
          '_1080p.m3u8'
        )}`
      }

      let audioSrc: string = ''
      if (validationData.audio_file) {
        const uploadBucket = await uploadToS3Bucket(
          validationData.audio_file,
          process.env.S3_BUCKET ?? 'hummusedu',
          user.email
        )

        if (!uploadBucket?.url) {
          return response.status(400).send({
            success: false,
            message: 'Failed to upload audio',
          })
        }

        audioSrc = uploadBucket.url
      }

      // Get existing video file if it exists
      const videoFile = await File.find(updateChapter.fileId ?? 0)

      const result = await db.transaction(async (trx) => {
        let fileId: number | undefined
        if (videoSrc) {
          if (videoFile) {
            videoFile.src = videoSrc
            videoFile.useTransaction(trx)
            await videoFile.save()
          } else {
            const chapterVideo = new File()
            chapterVideo.src = videoSrc
            chapterVideo.type = 'video'
            chapterVideo.userId = user.id
            chapterVideo.useTransaction(trx)
            await chapterVideo.save()
            fileId = chapterVideo.id
          }
        }

        if (validationData.title) {
          updateChapter.title = validationData.title
        }
        if (validationData.description) {
          updateChapter.description = validationData.description
        }
        if (validationData.story_id) {
          updateChapter.storyId = validationData.story_id
        }
        if (fileId) {
          updateChapter.fileId = fileId
        }
        if (validationData.fallback_chapter_id) {
          updateChapter.fallbackChapterId = validationData.fallback_chapter_id
        } else if (validationData.fallback_chapter_id === null) {
          updateChapter.fallbackChapterId = null
        }

        if (validationData.is_free_response !== undefined) {
          updateChapter.isFreeResponse = validationData.is_free_response
          if (validationData.is_free_response) {
            updateChapter.options = [{ value: 'free response' }]
          } else {
            updateChapter.options = validationData.options ? validationData.options : null
          }
        } else if (validationData.options) {
          updateChapter.options = validationData.options
        }

        if (audioSrc) {
          updateChapter.audioUrl = audioSrc
        }

        updateChapter.useTransaction(trx)
        await updateChapter.save()

        return updateChapter
      })

      return response.status(200).send({ success: true, data: result })
    } catch (error) {
      logger.error('Chapter update error: %o', error)
      return response.status(400).send(error)
    }
  }

  /**
   * Delete a chapter by ID
   */
  public async deleteChapter({ params, response }: HttpContext) {
    try {
      const deleteChapter = await Chapter.findOrFail(params.id)
      await deleteChapter.delete()

      return response.status(200).send({ success: true })
    } catch (error) {
      logger.error('Chapter delete error: %o', error)
      return response.status(400).send(error)
    }
  }
}
