import type { HttpContext } from '@adonisjs/core/http'
import { omit } from 'radash'

import Child from '#models/child'
import { addChildValidator, editChildValidator } from '#validators/child_validator'

export default class ChildrenController {
  public async findMyChildren({ auth, response, request }: HttpContext) {
    const user = await auth.authenticate()
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:asc').split(':')
    const filters = omit(request.all(), ['page', 'limit', 'sort'])

    console.log(user.id)
    const children = await Child.filter(filters)
      .where('user_id', user.id)
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.ok(children)
  }

  public async findChildren({ response, request, params }: HttpContext) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:asc').split(':')
    const filters = omit(request.all(), ['page', 'limit', 'sort'])

    const children = await Child.filter(filters)
      .where('user_id', params.user_id)
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.ok(children)
  }

  public async addChild({ response, request, auth }: HttpContext) {
    const user = await auth.authenticate()
    const validateData = await request.validateUsing(addChildValidator)

    const child = await Child.create({ ...validateData, userId: user.id })

    return response.ok({ success: true, data: child })
  }

  public async editChild({ response, request, params, auth }: HttpContext) {
    const user = await auth.authenticate()
    const validateData = await request.validateUsing(editChildValidator)

    const findChild = await Child.query()
      .where('user_id', user.id)
      .where('id', params.id)
      .firstOrFail()
    findChild.merge(validateData)
    await findChild.save()

    return response.ok({ success: true, data: findChild })
  }

  public async removeChild({ response, params, auth }: HttpContext) {
    const user = await auth.authenticate()
    const findChild = await Child.query()
      .where('user_id', user.id)
      .where('id', params.id)
      .firstOrFail()

    await findChild.delete()

    return response.ok({ success: true, data: findChild })
  }
}
