import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import db from '@adonisjs/lucid/services/db'

import Credit from '#models/credit'
import { updateCreditValidator } from '#validators/credit_validator'

export default class CreditsController {
  /**
   * Get all credits with pagination and sorting
   */
  public async findAll({ request, response }: HttpContext) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'rank:asc').split(':')

      const credits = await Credit.query().orderBy(sort[0], sort[1]).paginate(page, limit)

      return response.status(200).send(credits)
    } catch (error) {
      logger.error('Credits findAll error: %o', error)
      return response.status(400).send(error)
    }
  }

  /**
   * Update a credit by ID
   */
  public async update({ request, response, params }: HttpContext) {
    try {
      const validationData = await request.validateUsing(updateCreditValidator)
      const findCredit = await Credit.findOrFail(params.id)

      const result = await db.transaction(async (trx) => {
        findCredit.title = validationData.title
        if (validationData.description) {
          findCredit.description = validationData.description
        } else {
          findCredit.description = ''
        }
        findCredit.amount = validationData.amount
        findCredit.price = validationData.price
        findCredit.compareAt = validationData.compare_at
        findCredit.useTransaction(trx)
        await findCredit.save()

        return findCredit
      })

      return response.status(200).send({ success: true, data: result })
    } catch (error) {
      logger.error('Credits update error: %o', error)
      return response.status(400).send(error)
    }
  }
}
