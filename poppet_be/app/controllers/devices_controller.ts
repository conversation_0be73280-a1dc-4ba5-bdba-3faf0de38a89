import type { HttpContext } from '@adonisjs/core/http'
import Device from '#models/device'
import User from '#models/user'
import { createDeviceTokenValidator } from '#validators/utility_validator'

export default class DevicesController {
  /**
   * Create or update device token for push notifications
   */
  async createMyToken({ response, request, auth }: HttpContext) {
    const validationData = await request.validateUsing(createDeviceTokenValidator)

    const user = await User.findOrFail(auth?.user?.id)
    console.log('debug device: ', validationData)
    console.log('user id : ', user.id)
    
    const device = await Device.updateOrCreate(
      {
        userId: user.id,
        platform: validationData.platform,
        deviceNo: validationData.device_no,
        model: validationData.model,
      },
      {
        token: validationData.token,
      }
    )
    
    return response.send({
      success: true,
      data: await Device.query().where('id', device.id).first(),
    })
  }
}
