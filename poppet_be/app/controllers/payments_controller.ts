import type { HttpContext } from '@adonisjs/core/http'
import { androidpublisher, auth } from '@googleapis/androidpublisher'
import db from '@adonisjs/lucid/services/db'
import axios from 'axios'
import BigNumber from 'bignumber.js'
import jwt, { JwtHeader, JwtPayload } from 'jsonwebtoken'
import { DateTime } from 'luxon'

// Models
import Bundle from '#models/bundle'
import Credit from '#models/credit'
import Preschool from '#models/preschool'
import Story from '#models/story'
import StoryOrder from '#models/story_order'
import Transaction from '#models/transaction'
import Voucher from '#models/voucher'
import Wallet from '#models/wallet'

// Validators
import {
  waivedPurchaseValidator,
  validateAndroidPurchaseValidator,
  validateIOSPurchaseValidator,
  validateAndroidCreditsPurchaseValidator,
  validateIOSCreditsPurchaseValidator,
  purchaseWithCreditValidator,
} from '#validators/payment_validator'

// Helper function to get Android Publisher API client
const getAndroidPublisher = async () => {
  const serviceAccountAuth = new auth.GoogleAuth({
    keyFile: process.env.GOOGLE_APPLICATION_CREDENTIALS,
    scopes: ['https://www.googleapis.com/auth/androidpublisher'],
  })

  const authClient = await serviceAccountAuth.getClient()
  return androidpublisher({ version: 'v3', auth: authClient })
}

export default class PaymentsController {
  /**
   * Process waived purchase (free with voucher)
   */
  async waivedPurchase({ request, response, auth }: HttpContext) {
    try {
      const user = await auth.authenticate()
      const wallet = await Wallet.findByOrFail('user_id', user.id)
      const validationData = await request.validateUsing(waivedPurchaseValidator)

      let findVoucher: Voucher | null = null
      const findStory = await Story.find(validationData.story_id ?? 0)
      const findPreschool = await Preschool.find(validationData.preschool_id ?? 0)

      if (validationData.voucher_id) {
        findVoucher = await Voucher.query()
          .where('id', validationData.voucher_id)
          .where((query) => {
            query
              .whereNull('expired_at')
              .orWhere('expired_at', '>=', DateTime.local().toFormat('yyyy-MM-dd HH:mm:ss'))
          })
          .withCount('storyOrders', (query) => {
            query.countDistinct('user_id').as('total_of_used')
          })
          .first()

        if (!findVoucher) {
          return response.status(400).send({
            success: false,
            message: 'Invalid voucher or voucher expired.',
          })
        }

        if (findVoucher.$extras.total_of_used >= findVoucher.maxRedemption) {
          return response.status(400).send({
            success: false,
            message: 'Voucher redemption limit reached.',
          })
        }
      }

      const result = await db.transaction(async (trx) => {
        const unlockedStories: StoryOrder[] = []

        if (findStory) {
          // Handle single story purchase
          const existingOrder = await StoryOrder.query()
            .where('user_id', user.id)
            .where('story_id', findStory.id)
            .first()

          if (existingOrder) {
            return response.status(400).send({
              success: false,
              message: 'Story already purchased.',
            })
          }

          const newOrder = new StoryOrder()
          newOrder.storyId = findStory.id
          newOrder.voucherId = findVoucher?.id
          newOrder.userId = user.id
          newOrder.finalAmount = 0
          newOrder.discountAmount = findStory.price

          newOrder.useTransaction(trx)
          await newOrder.save()

          unlockedStories.push(newOrder)
        } else if (findPreschool) {
          // Handle preschool stories purchase
          const stories = await Story.query()
            .where('preschool_id', findPreschool.id)
            .where('status', 'active')

          for (const story of stories) {
            const existingOrder = await StoryOrder.query()
              .where('user_id', user.id)
              .where('story_id', story.id)
              .first()

            if (existingOrder) continue

            const newOrder = new StoryOrder()
            newOrder.storyId = story.id
            newOrder.voucherId = findVoucher?.id
            newOrder.userId = user.id
            newOrder.finalAmount = 0
            newOrder.discountAmount = story.price

            newOrder.useTransaction(trx)
            await newOrder.save()

            unlockedStories.push(newOrder)
          }
        }

        return unlockedStories
      })

      return response.status(200).send({ success: true, data: result })
    } catch (error) {
      console.log(error)
      return response.status(400).send({
        success: false,
        error: error.message || 'Purchase failed',
      })
    }
  }

  /**
   * Validate Android purchase
   */
  async validatePurchaseAndroid({ request, response, auth }: HttpContext) {
    try {
      console.log('validate android purchase')
      console.log(request.all())
      const user = await auth.authenticate()
      const validationData = await request.validateUsing(validateAndroidPurchaseValidator)

      const findStory = await Story.findByOrFail('handle', validationData.handle)
      const play = await getAndroidPublisher()

      const { data: productPurchase } = await play.purchases.products.get({
        packageName: process.env.GOOGLE_PACKAGE_NAME,
        productId: findStory.handle,
        token: validationData.purchase_token,
      })

      if (productPurchase.purchaseState === 1) {
        // Purchase completed
        const existingOrder = await StoryOrder.query()
          .where('user_id', user.id)
          .where('story_id', findStory.id)
          .first()

        if (existingOrder) {
          return response.status(400).send({
            success: false,
            message: 'Story already purchased.',
          })
        }

        const newOrder = await StoryOrder.create({
          storyId: findStory.id,
          userId: user.id,
          finalAmount: findStory.price,
          discountAmount: 0,
          purchaseToken: validationData.purchase_token,
          platform: 'android',
        })

        return response.status(200).send({
          success: true,
          data: newOrder,
        })
      }

      return response.status(400).send({
        success: false,
        message: 'Purchase not completed.',
      })
    } catch (error) {
      console.log(error)
      return response.status(400).send({
        success: false,
        error: error.message || 'Purchase validation failed',
      })
    }
  }

  /**
   * Validate iOS purchase
   */
  async validatePurchaseIOS({ request, response, auth }: HttpContext) {
    try {
      console.log('validate ios purchase')
      console.log(request.all())
      const user = await auth.authenticate()
      const validationData = await request.validateUsing(validateIOSPurchaseValidator)

      const findStory = await Story.findByOrFail('handle', validationData.handle)

      const now = DateTime.local()
      const jwtHeaders: JwtHeader = {
        alg: 'ES256',
        kid: process.env.IOS_API_KEY,
        typ: 'JWT',
      }

      const jwtPayload: JwtPayload = {
        iss: process.env.IOS_ISSUER_ID,
        iat: now.toUnixInteger(),
        exp: now.plus({ minutes: 5 }).toUnixInteger(),
        aud: 'appstoreconnect-v1',
        bid: process.env.IOS_BUNDLE_ID,
      }

      const token = jwt.sign(jwtPayload, process.env.IOS_PRIVATE_KEY!, {
        algorithm: 'ES256',
        header: jwtHeaders,
      })

      const transactionResponse = await axios.get(
        `https://api.storekit.itunes.apple.com/inApps/v1/transactions/${validationData.transaction_id}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      )

      if (transactionResponse.status === 200) {
        const existingOrder = await StoryOrder.query()
          .where('user_id', user.id)
          .where('story_id', findStory.id)
          .first()

        if (existingOrder) {
          return response.status(400).send({
            success: false,
            message: 'Story already purchased.',
          })
        }

        const newOrder = await StoryOrder.create({
          storyId: findStory.id,
          userId: user.id,
          finalAmount: findStory.price,
          discountAmount: 0,
          transactionId: validationData.transaction_id,
          platform: 'ios',
        })

        return response.status(200).send({
          success: true,
          data: newOrder,
        })
      }

      return response.status(400).send({
        success: false,
        message: 'Purchase not completed.',
      })
    } catch (error) {
      console.log(error)
      return response.status(400).send({
        success: false,
        error: error.message || 'Purchase validation failed',
      })
    }
  }
}
