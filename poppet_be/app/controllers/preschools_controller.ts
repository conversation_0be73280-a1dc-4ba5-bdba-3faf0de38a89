import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'
import db from '@adonisjs/lucid/services/db'

import Preschool from '#models/preschool'
import User from '#models/user'
import { createPreschoolValidator, updatePreschoolValidator } from '#validators/preschool_validator'

export default class PreschoolsController {
  /**
   * Get all preschools (public endpoint)
   */
  public async findAll({ request, response, auth }: HttpContext) {
    try {
      const user = await User.find(auth?.user?.id ?? 0)

      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'id:desc').split(':')

      if (user === null) {
        const preschools = await Preschool.query()
          .where('blocked', false)
          .preload('stories', (query) => query.orderBy('ordering', 'asc'))
          .orderBy(sort[0], sort[1])
          .paginate(page, limit)
        return response.ok(preschools)
      }

      const preschools = await Preschool.query()
        .where('blocked', false)
        .preload('stories', (query) =>
          query
            .withCount('storyOrders', (query) =>
              query.where('user_id', user.id).where('blocked', false).as('redeemed')
            )
            .orderBy('ordering', 'asc')
        )
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.ok(preschools)
    } catch (error) {
      logger.error('Preschools findAll error: %o', error)
      return response.badRequest(error)
    }
  }

  /**
   * Get all preschools (admin endpoint)
   */
  public async adminFind({ request, response }: HttpContext) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'id:desc').split(':')

      const preschools = await Preschool.query().orderBy(sort[0], sort[1]).paginate(page, limit)

      return response.ok(preschools)
    } catch (error) {
      logger.error('Preschools adminFind error: %o', error)
      return response.badRequest(error)
    }
  }

  /**
   * Get a single preschool by ID (admin endpoint)
   */
  public async adminFindOne({ response, params }: HttpContext) {
    try {
      const preschools = await Preschool.query().where('id', params.id).preload('stories')

      return response.ok(preschools)
    } catch (error) {
      logger.error('Preschool adminFindOne error: %o', error)
      return response.badRequest(error)
    }
  }

  /**
   * Create a new preschool
   */
  public async create({ request, response }: HttpContext) {
    try {
      const validationData = await request.validateUsing(createPreschoolValidator)

      const result = await db.transaction(async (trx) => {
        const newPreschool = new Preschool()
        newPreschool.title = validationData.title
        if (validationData.description) {
          newPreschool.description = validationData.description
        }
        newPreschool.region = validationData.region
        newPreschool.language = validationData.language
        newPreschool.blocked = validationData.blocked

        newPreschool.useTransaction(trx)
        await newPreschool.save()

        return newPreschool
      })

      return response.ok({ success: true, data: result })
    } catch (error) {
      logger.error('Preschool create error: %o', error)
      return response.badRequest(error)
    }
  }

  /**
   * Update a preschool by ID
   */
  public async update({ request, response, params }: HttpContext) {
    try {
      const preschool = await Preschool.findOrFail(params.id)
      const validationData = await request.validateUsing(updatePreschoolValidator)

      const result = await db.transaction(async (trx) => {
        preschool.title = validationData.title
        if (validationData.description) {
          preschool.description = validationData.description
        }
        preschool.region = validationData.region
        preschool.language = validationData.language
        preschool.blocked = validationData.blocked

        preschool.useTransaction(trx)
        await preschool.save()

        return preschool
      })

      return response.ok({ success: true, data: result })
    } catch (error) {
      logger.error('Preschool update error: %o', error)
      return response.badRequest(error)
    }
  }
}
