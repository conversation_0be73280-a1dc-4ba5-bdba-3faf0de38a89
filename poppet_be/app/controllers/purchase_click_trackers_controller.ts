import type { HttpContext } from '@adonisjs/core/http'
import db from '@adonisjs/lucid/services/db'
import PurchaseClickTracker from '#models/purchase_click_tracker'
import User from '#models/user'
import { clickTrackerValidator } from '#validators/purchase_click_tracker_validator'

export default class PurchaseClickTrackersController {
  /**
   * Track purchase click for analytics
   */
  async clickTracker({ request, response, auth }: HttpContext) {
    try {
      const user = await User.findOrFail(auth.user?.id)
      const validationData = await request.validateUsing(clickTrackerValidator)

      await db.transaction(async (trx) => {
        await PurchaseClickTracker.updateOrCreate(
          {
            storyId: validationData.story_id,
            userId: user.id,
          },
          {},
          { client: trx }
        )
      })

      return response.status(200).send({
        success: true,
      })
    } catch (error) {
      console.log(error)
      return response.status(400).send({
        success: false,
        error: error.message || 'Failed to track click',
      })
    }
  }
}
