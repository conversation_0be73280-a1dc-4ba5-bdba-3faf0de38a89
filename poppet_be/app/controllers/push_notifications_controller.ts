import type { HttpContext } from '@adonisjs/core/http'
import PushNotification from '#models/push_notification'
import User from '#models/user'
import emitter from '@adonisjs/core/services/emitter'
import { uploadToS3Bucket } from './files_controller.js'
import { sendAppNotificationValidator } from '#validators/push_notification_validator'
import { parse } from 'csv-parse'
import fs from 'fs'

export default class PushNotificationsController {
  /**
   * Get paginated list of push notifications
   */
  async find({ request, response }: HttpContext) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')

    const notifications = await PushNotification.query()
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.status(200).send(notifications)
  }

  /**
   * Get a specific push notification
   */
  async findOne({ response, params }: HttpContext) {
    const notification = await PushNotification.query().where('id', params.id).first()

    return response.send({
      data: notification,
    })
  }

  /**
   * Send app notification (admin only)
   */
  async sendAppNotification({ response, request, auth }: HttpContext) {
    try {
      console.log('sendAppNotification', request.all())
      const user = await User.findOrFail(auth.user?.id)

      const validationData = await request.validateUsing(sendAppNotificationValidator)

      const recipients: number[] = []
      const emails: string[] = []

      const parser = parse({
        columns: true,
        skip_empty_lines: true,
      })

      if (fs.existsSync(validationData.recipients.tmpPath ?? '')) {
        const fsStream = fs.createReadStream(validationData.recipients.tmpPath!)
        await new Promise((resolve) => {
          fsStream
            .pipe(parser)
            .on('data', (data) => {
              if (data.email) {
                emails.push(data.email)
              } else {
                throw new Error('wrong csv format')
              }
            })
            .on('end', async () => {
              if (emails.length <= 0) {
                throw new Error('wrong csv format')
              }
              const users = await User.query()
                .whereIn('email', emails)
                .whereHas('devices', (query) => query.whereNotNull('token'))
              for (const user of users) {
                recipients.push(user.id)
              }
              console.log(recipients)
              resolve(true)
            })
        })
      }

      let src: string | null = null
      if (validationData.image) {
        const uploadBucket = await uploadToS3Bucket(
          validationData.image,
          process.env.S3_BUCKET ?? 'hummusedu',
          user.email
        )

        if (!uploadBucket?.url) {
          return response.status(400).send({
            success: false,
            message: 'Failed to upload',
          })
        }

        src = uploadBucket.url
      }

      // Create new push notification record
      const newMsg = new PushNotification()
      newMsg.title = validationData.title
      newMsg.body = validationData?.body ?? ''
      newMsg.imageUrl = src ?? ''
      newMsg.recipients = recipients
      await newMsg.save()

      console.log({
        user_ids: recipients,
        title: validationData.title,
        body: validationData.body,
        image_url: src,
        id: newMsg.id,
      })

      emitter.emit('send:messages', {
        user_ids: recipients,
        title: validationData.title,
        body: validationData.body,
        image_url: src,
        id: newMsg.id,
      })

      return response.send({ success: true, data: newMsg })
    } catch (error) {
      console.log(error)
      return response.status(400).send(error)
    }
  }
}
