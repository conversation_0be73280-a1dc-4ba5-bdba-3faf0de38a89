import type { HttpContext } from '@adonisjs/core/http'
import RecordingResult from '#models/recording_result'
import File from '#models/file'
import Chapter from '#models/chapter'
import emitter from '@adonisjs/core/services/emitter'
import { uploadToS3Bucket } from './files_controller.js'
import { createRecordingResultValidator } from '#validators/recording_result_validator'
import { omit } from 'radash'

export default class RecordingResultsController {
  /**
   * Get paginated list of recording results with filtering
   */
  async find({ response, request }: HttpContext) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = omit(request.all(), ['page', 'sort', 'limit'])

    const recordingResults = await RecordingResult.filter(filters)
      .preload('user')
      .preload('story')
      .preload('chapter')
      .preload('file')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.status(200).send(recordingResults)
  }

  /**
   * Get a specific recording result
   */
  async findOne({ response, params }: HttpContext) {
    const recordingResult = await RecordingResult.query()
      .where('id', params.id)
      .preload('file')
      .preload('user')
      .preload('chapter', (query) => query.preload('story'))
      .firstOrFail()

    return response.status(200).send({
      data: recordingResult,
    })
  }

  /**
   * Create a new recording result
   */
  async create({ auth, response, request }: HttpContext) {
    const user = await auth.authenticate()
    const validationData = await request.validateUsing(createRecordingResultValidator)

    const findChapter = await Chapter.query()
      .where('id', validationData.chapter_id)
      .preload('story')
      .firstOrFail()

    let recordingFile: File | null = null

    // Upload file to S3 if provided
    if (validationData.file) {
      const uploadBucket = await uploadToS3Bucket(
        validationData.file,
        process.env.S3_BUCKET ?? 'hummusedu',
        user.email
      )

      // Create file record
      recordingFile = await File.create({
        src: uploadBucket.url,
        type: 'recording',
        userId: user.id,
      })
    }

    // Trigger first start activity event for community stories
    if (findChapter.story.isCommunity && findChapter.story.defaultChapterId === findChapter.id) {
      const findRecording = await RecordingResult.query()
        .where('story_id', findChapter.storyId)
        .where('chapter_id', findChapter.id)
        .where('user_id', user.id)
        .first()

      if (!findRecording) {
        // Trigger first start event
        emitter.emit('activity:start', {
          email: user.email,
        })
      }
    }

    let categories = validationData.category?.join(', ')
    const recordingResponse = await RecordingResult.create({
      fileId: recordingFile?.id,
      category: categories ?? '',
      device: validationData.device,
      language: validationData.language,
      userId: user.id,
      chapterId: validationData.chapter_id,
      storyId: findChapter.storyId,
      calibrations: validationData.calibration,
      volumes: validationData.actual_volume,
      minimumVolume: validationData.minimum_volume,
    })

    return response.status(200).send({
      data: recordingResponse,
    })
  }
}
