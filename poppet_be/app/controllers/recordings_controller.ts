import type { HttpContext } from '@adonisjs/core/http'
import { v4 as uuid } from 'uuid'
import axios from 'axios'
import fs from 'fs'
import AWS from 'aws-sdk'
import { omit } from 'radash'
import { DateTime } from 'luxon'
import util from 'util'
import { exec } from 'child_process'

// Models
import File from '#models/file'
import RecordingResult from '#models/recording_result'
import AzureAuth from '#models/azure_auth'
import Story, { StoryType } from '#models/story'
import Chapter from '#models/chapter'
import Session from '#models/session'

// Services and utilities
import { uploadToS3Bucket } from './files_controller.js'
import {
  storeRecordingValidator,
  instantStoreRecordingValidator,
  findRecordingValidator,
  instantFindRecordingValidator,
} from '#validators/recording_validator'

// Microsoft Cognitive Services Speech SDK
import {
  SpeechConfig,
  AudioConfig,
  SpeechRecognizer,
  PronunciationAssessmentResult,
  PronunciationAssessmentConfig,
  PronunciationAssessmentGradingSystem,
} from 'microsoft-cognitiveservices-speech-sdk'

// Types
interface Option {
  answer?: boolean
  value: string
  threshold?: number
}

const execPromise = util.promisify(exec)

export default class RecordingsController {
  /**
   * Store a recording and process it
   */
  async store({ auth, request, response }: HttpContext) {
    try {
      const validationData = await request.validateUsing(storeRecordingValidator)
      const user = await auth.authenticate()

      // Upload file to S3
      const uploadBucket = await uploadToS3Bucket(
        validationData.file,
        process.env.S3_BUCKET ?? 'hummusedu',
        user.email
      )

      if (!uploadBucket?.url) {
        return response.status(400).send({
          success: false,
          message: 'Failed to upload',
        })
      }

      // Create file record
      const recordingFile = await File.create({
        src: uploadBucket.url,
        type: 'recording',
        userId: user.id,
      })

      // Process with Azure Speech Services
      const azureAuth = await AzureAuth.query().orderBy('id', 'desc').first()
      if (!azureAuth) {
        return response.status(400).send({
          success: false,
          message: 'Azure auth not configured',
        })
      }

      // Configure speech recognition
      const speechConfig = SpeechConfig.fromSubscription(
        azureAuth.subscriptionKey,
        azureAuth.serviceRegion
      )
      speechConfig.speechRecognitionLanguage = validationData.language

      // Download and process audio file
      const audioPath = `/tmp/${uuid()}.wav`
      const s3 = new AWS.S3({
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
        region: process.env.AWS_DEFAULT_REGION,
      })

      const params = {
        Bucket: process.env.S3_BUCKET ?? 'hummusedu',
        Key: uploadBucket.key,
      }

      const data = await s3.getObject(params).promise()
      fs.writeFileSync(audioPath, data.Body as Buffer)

      // Convert audio format if needed
      const convertedPath = `/tmp/${uuid()}_converted.wav`
      await execPromise(
        `ffmpeg -i ${audioPath} -acodec pcm_s16le -ac 1 -ar 16000 ${convertedPath}`
      )

      // Configure audio and pronunciation assessment
      const audioConfig = AudioConfig.fromWavFileInput(fs.readFileSync(convertedPath))
      const pronunciationConfig = new PronunciationAssessmentConfig(
        validationData.category?.join(' ') || '',
        PronunciationAssessmentGradingSystem.HundredMark,
        'Comprehensive',
        true
      )

      // Create speech recognizer
      const recognizer = new SpeechRecognizer(speechConfig, audioConfig)
      pronunciationConfig.applyTo(recognizer)

      // Perform recognition
      const result = await new Promise((resolve, reject) => {
        recognizer.recognizeOnceAsync(
          (result) => {
            const pronunciationResult = PronunciationAssessmentResult.fromResult(result)
            resolve({
              text: result.text,
              pronunciationResult,
              result,
            })
          },
          (error) => reject(error)
        )
      })

      // Clean up temporary files
      fs.unlinkSync(audioPath)
      fs.unlinkSync(convertedPath)

      // Create recording result
      const recordingResult = await RecordingResult.create({
        fileId: recordingFile.id,
        category: validationData.category?.join(', ') || '',
        device: validationData.device,
        language: validationData.language,
        userId: user.id,
        chapterId: validationData.chapter_id,
        storyId: validationData.story_id,
        sessionId: validationData.session_id,
        calibrations: validationData.calibration,
        volumes: validationData.actual_volume,
        minimumVolume: validationData.minimum_volume,
        rawResult: result,
      })

      // Handle game scoring if applicable
      if (validationData.story_id) {
        const story = await Story.findOrFail(validationData.story_id)
        if (story.type === StoryType.GAME && validationData.session_id) {
          const session = await Session.findOrFail(validationData.session_id)
          const chapter = await Chapter.findOrFail(validationData.chapter_id!)

          const options = chapter.options as Option[]
          const correctOption = options.findIndex(
            (option) =>
              option.answer &&
              option.value === recordingResult.category &&
              recordingResult.hypothesisScore >= (option?.threshold ?? 0)
          )

          if (correctOption !== -1) {
            session.gameScore += 1
          }
          session.fullGameScore += 1
          await session.save()
        }
      }

      return response.status(200).send({
        success: true,
        data: recordingResult,
      })
    } catch (error) {
      console.log('Recording store error:', error)
      return response.status(400).send({
        success: false,
        error: error.message || 'Processing failed',
      })
    }
  }

  /**
   * Store recording instantly (simplified version)
   */
  async instantStore({ request, response }: HttpContext) {
    try {
      const validationData = await request.validateUsing(instantStoreRecordingValidator)

      // Upload file to S3
      const uploadBucket = await uploadToS3Bucket(
        validationData.file,
        process.env.S3_BUCKET ?? 'hummusedu',
        'instant'
      )

      if (!uploadBucket?.url) {
        return response.status(400).send({
          success: false,
          message: 'Failed to upload',
        })
      }

      // Create file record without user association for instant recordings
      const recordingFile = await File.create({
        src: uploadBucket.url,
        type: 'recording',
        userId: null, // Instant recordings don't have user association
      })

      // Create basic recording result
      const recordingResult = await RecordingResult.create({
        fileId: recordingFile.id,
        category: validationData.category?.join(', ') || '',
        device: validationData.device,
        language: validationData.language,
        chapterId: validationData.chapter_id,
        storyId: validationData.story_id,
        sessionId: validationData.session_id,
        calibrations: validationData.calibration || [],
        volumes: validationData.actual_volume || [],
        minimumVolume: validationData.minimum_volume,
      })

      return response.status(200).send({
        success: true,
        data: recordingResult,
      })
    } catch (error) {
      console.log('Instant store error:', error)
      return response.status(400).send({
        success: false,
        error: error.message || 'Processing failed',
      })
    }
  }

  /**
   * Find recording result by status URI
   */
  async find({ auth, request, response }: HttpContext) {
    const user = await auth.authenticate()

    try {
      const validationData = await request.validateUsing(findRecordingValidator)

      const res = await axios.get(validationData.status_uri)

      const highestScore = Math.max.apply(
        Math,
        res.data.results.map((result: any) => result.hypothesis_score)
      )

      const findRecordingResult = await RecordingResult.query()
        .where('file_id', validationData.recording_id)
        .first()

      if (findRecordingResult) {
        // Update existing result
        findRecordingResult.rawResult = res.data
        findRecordingResult.hypothesisScore = highestScore
        findRecordingResult.hypothesis = res.data.results[0]?.hypothesis || ''
        await findRecordingResult.save()
      } else {
        // Create new result
        await RecordingResult.create({
          rawResult: res.data,
          fileId: validationData.recording_id,
          language: res.data.language_code,
          userId: user.id,
          chapterId: validationData.chapter_id,
          device: validationData.device,
          calibrations: validationData.calibrations,
          volumes: validationData.volumes,
          minimumVolume: validationData.minimum_volume,
          sessionId: validationData.session_id,
        })
      }

      return response.status(200).send({
        data: res.data,
      })
    } catch (error) {
      console.log('Find error:', error)
      if (error.response?.status === 404) {
        return response.status(400).send({
          code: 'request.not.ready',
        })
      }

      return response.status(400).send(error)
    }
  }

  /**
   * Find recording result instantly by status URI
   */
  async instantFind({ request, response }: HttpContext) {
    try {
      const validationData = await request.validateUsing(instantFindRecordingValidator)

      const res = await axios.get(validationData.status_uri)

      const highestScore = Math.max.apply(
        Math,
        res.data.results.map((result: any) => result.hypothesis_score)
      )

      const findRecordingResult = await RecordingResult.query()
        .where('file_id', validationData.recording_id)
        .first()

      if (findRecordingResult) {
        // Update existing result
        findRecordingResult.rawResult = res.data
        findRecordingResult.hypothesisScore = highestScore
        findRecordingResult.hypothesis = res.data.results[0]?.hypothesis || ''
        await findRecordingResult.save()
      } else {
        // Create new result
        await RecordingResult.create({
          rawResult: res.data,
          fileId: validationData.recording_id,
          language: res.data.language_code,
          chapterId: validationData.chapter_id,
          device: validationData.device,
          calibrations: validationData.calibrations || [],
          volumes: validationData.volumes || [],
          minimumVolume: validationData.minimum_volume,
        })
      }

      return response.status(200).send({
        data: res.data,
      })
    } catch (error) {
      console.log('Instant find error:', error)
      if (error.response?.status === 404) {
        return response.status(400).send({
          code: 'request.not.ready',
        })
      }

      return response.status(400).send(error)
    }
  }
}
