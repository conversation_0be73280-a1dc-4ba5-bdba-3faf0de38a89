import type { HttpContext } from '@adonisjs/core/http'
import Review from '#models/review'
import { createReviewValidator, updateReviewValidator } from '#validators/utility_validator'
import { omit, camelCase } from 'radash'

export default class ReviewsController {
  /**
   * Get reviews with filtering and pagination
   */
  async find({ request, response }: HttpContext) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = omit(request.all(), ['page', 'sort', 'limit'])

    const reviews = await Review.filter(filters)
      .preload('user')
      .preload('story')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.status(200).send(reviews)
  }

  /**
   * Get a specific review
   */
  async findOne({ params, response }: HttpContext) {
    const review = await Review.query().where('id', params.id).firstOrFail()

    return response.status(200).send({ data: review })
  }

  /**
   * Create a new review
   */
  async create({ auth, request, response }: HttpContext) {
    const user = await auth.authenticate()
    const validationData = await request.validateUsing(createReviewValidator)

    const { user_id: userId, ...rest } = validationData

    const toCreate = {}

    if (userId) {
      // admin create
      toCreate['userId'] = userId
    } else {
      // user submit
      toCreate['userId'] = user.id
    }

    for (let key of Object.keys(rest)) {
      toCreate[camelCase(key)] = rest[key]
    }

    const createReview = await Review.create({
      ...toCreate,
    })

    return response.status(200).send({
      success: true,
      data: createReview,
    })
  }

  /**
   * Update an existing review
   */
  async update({ request, response, params }: HttpContext) {
    const validationData = await request.validateUsing(updateReviewValidator)

    const review = await Review.findByOrFail('id', params.id)

    for (let key of Object.keys(validationData)) {
      review[camelCase(key)] = validationData[key]
    }

    await review.save()

    return response.status(200).send({
      success: true,
      data: review,
    })
  }

  /**
   * Delete a review
   */
  async delete({ params, response }: HttpContext) {
    const review = await Review.findByOrFail('id', params.id)
    await review.delete()

    return response.status(200).send({ success: true })
  }
}
