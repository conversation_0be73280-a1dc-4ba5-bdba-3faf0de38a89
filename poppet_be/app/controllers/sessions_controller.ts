import type { HttpContext } from '@adonisjs/core/http'
import Session from '#models/session'
import Story, { StoryType } from '#models/story'
import StoryOrder from '#models/story_order'
import UserPack from '#models/user_pack'
import { DateTime } from 'luxon'
import { createSessionValidator } from '#validators/session_validator'

export default class SessionsController {
  /**
   * Create a new session
   */
  async create({ auth, request, response }: HttpContext) {
    const user = await auth.authenticate()
    const validateData = await request.validateUsing(createSessionValidator)

    const createSession = await Session.create({
      ...validateData,
      userId: user.id,
      childId: user.activeChildId,
    })

    return response.status(200).send({ success: true, data: createSession })
  }

  /**
   * End a session and update related story/pack statuses
   */
  async endSession({ auth, response, params }: HttpContext) {
    try {
      const user = await auth.authenticate()

      const findSession = await Session.query()
        .where('user_id', user.id)
        .where('id', params.id)
        .preload('story')
        .firstOrFail()

      findSession.endedAt = DateTime.now()
      await findSession.save()

      if (findSession.story.isCommunity || findSession.story.type !== StoryType.PACK) {
        // Handle community stories or non-pack stories
        const findStoryOrder = await StoryOrder.firstOrNew({
          userId: user.id,
          storyId: findSession.storyId,
        })

        const currentStatusList = findStoryOrder.storyStatuses ?? []
        const findIndex = currentStatusList.findIndex(
          (item) => item.child_id === user.activeChildId && item.story_id === findSession.storyId
        )

        if (findIndex === -1) {
          currentStatusList.push({
            story_id: findSession.storyId,
            child_id: user.activeChildId,
            completed: true,
          })
          findStoryOrder.storyStatuses = currentStatusList
          await findStoryOrder.save()
        }
      } else {
        // Handle pack stories
        const findUserPack = await UserPack.query()
          .where('user_id', user.id)
          .where('pack_id', findSession.story.packId)
          .firstOrFail()

        // Update story status
        const currentStoryStatusList = findUserPack.storyStatuses ?? []
        const findStoryIndex = currentStoryStatusList.findIndex(
          (item) => item.child_id === user.activeChildId && item.story_id === findSession.storyId
        )

        if (findStoryIndex === -1) {
          currentStoryStatusList.push({
            story_id: findSession.storyId,
            child_id: user.activeChildId,
            completed: true,
          })
          findUserPack.storyStatuses = currentStoryStatusList
        }

        // Update level status if all stories in level are completed
        const currentLevelStatusList = findUserPack.levelStatuses ?? []
        const findLevelIndex = currentLevelStatusList.findIndex(
          (item) => item.child_id === user.activeChildId && item.level === findSession.story.level
        )

        if (findLevelIndex === -1) {
          const findPackLevelStories = await Story.query()
            .where('pack_id', findSession.story.packId)
            .where('level', findSession.story.level)

          const filteredChildStatusList = currentStoryStatusList.filter(
            (item) =>
              item.child_id === user.activeChildId &&
              findPackLevelStories.some((story) => story.id === item.story_id)
          )

          if (findPackLevelStories.length === filteredChildStatusList.length) {
            currentLevelStatusList.push({
              child_id: user.activeChildId,
              level: findSession.story.level,
              completed: true,
              triggered: false,
            })

            findUserPack.levelStatuses = currentLevelStatusList
          }
        }
        await findUserPack.save()
      }

      return response.status(200).send({ success: true, data: findSession })
    } catch (error) {
      console.log(error)
      return response.badRequest({ success: false, error: error.message ?? 'Server Error' })
    }
  }
}
