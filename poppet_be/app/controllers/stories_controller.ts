import type { HttpContext } from '@adonisjs/core/http'
import { omit, keys } from 'radash'

import Story from '#models/story'
import User from '#models/user'

import { createStoryValidator, updateStoryValidator } from '#validators/story_validator'

export default class StoriesController {
  public async findAll({ auth, request, response }: HttpContext) {
    const user = await User.find(auth.user?.id ?? 0)
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:asc').split(':')
    const isCommunity = request.input('is_community', 'false')
    const type = request.input('type', 'community')
    const filters = omit(request.all(), ['page', 'limit', 'sort'])

    if (!keys(filters).includes('region')) {
      filters['region'] = 'sg'
    }

    if (keys(filters).includes('status')) {
      filters['status'] = filters['status'].split(',')
    }

    if (keys(filters).includes('type')) {
      filters['type'] = filters['type'].split(',')
    }

    if (keys(filters).includes('exclude_redeemed')) {
      if (filters.exclude_redeemed === 'true') {
        if (user) {
          filters.exclude_redeemed = user.id
        } else {
          delete filters.exclude_redeemed
        }
      } else if (filters.exclude_redeemed === 'false') {
        delete filters.exclude_redeemed
      }
    }

    // if logged in return user-preloaded packs
    // otherwise return all packs
    let stories: Story[]
    if (user === null) {
      if (type === 'community' || isCommunity === 'true') {
        stories = await Story.filter(filters)
          .whereNot('status', 'closed')
          .orderByRaw("FIELD(status, 'active', 'upcoming') asc, id desc")
          .paginate(page, limit)
      } else {
        stories = await Story.filter(filters)
          .whereNot('status', 'closed')
          .orderBy(sort[0], sort[1])
          .paginate(page, limit)
      }
      return response.ok(stories)
    }

    if (type === 'community' || isCommunity === 'true') {
      stories = await Story.filter(filters)
        .whereNot('status', 'closed')
        .withCount('storyOrders', (query) =>
          query.where('user_id', user.id).where('blocked', false).as('redeemed')
        )
        .preload('storyOrders', (query) => query.where('user_id', user.id).where('blocked', false))
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)
    } else {
      stories = await Story.filter(filters)
        .whereNot('status', 'closed')
        .withCount('storyOrders', (query) =>
          query.where('user_id', user.id).where('blocked', false).as('redeemed')
        )
        .preload('storyOrders', (query) => query.where('user_id', user.id).where('blocked', false))
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)
    }

    return response.ok(stories)
  }

  public async adminFind({ response, request }: HttpContext) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = omit(request.all(), ['page', 'limit', 'sort'])

    if (keys(filters).includes('status')) {
      filters['status'] = filters['status'].split(',')
    }

    if (keys(filters).includes('type')) {
      filters['type'] = filters['type'].split(',')
    }

    const stories = await Story.filter(filters)
      .preload('previewVideo')
      .preload('previewImage')
      .preload('thumbnail')
      .preload('defaultChapter')
      .preload('tags')
      .withCount('chapters')
      .withCount('storyOrders', (query) => query.countDistinct('user_id').as('total_of_used'))
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.ok(stories)
  }

  public async findMyStories({ request, response, auth }: HttpContext) {
    const user = await User.findOrFail(auth.user?.id)
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = omit(request.all(), ['page', 'limit', 'sort'])

    if (keys(filters).includes('status')) {
      filters['status'] = filters['status'].split(',')
    }

    if (keys(filters).includes('type')) {
      filters['type'] = filters['type'].split(',')
    }

    const stories = await Story.filter(filters)
      .where('user_id', user.id)
      .preload('previewVideo')
      .preload('previewImage')
      .preload('thumbnail')
      .preload('defaultChapter')
      .preload('tags')
      .withCount('chapters')
      .withCount('storyOrders', (query) => query.countDistinct('user_id').as('total_of_used'))
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.ok(stories)
  }

  public async create({ request, response, auth }: HttpContext) {
    await auth.authenticate()
    const validationData = await request.validateUsing(createStoryValidator)

    try {
      // Handle file uploads and create story
      const storyData = {
        ...validationData,
        userId: auth.user?.id,
        handle: validationData.title.toLowerCase().replace(/\s+/g, '-'),
      }

      const result = await Story.create(storyData)

      return response.ok({ success: true, data: result })
    } catch (error) {
      return response.badRequest({
        success: false,
        message: 'Failed to create story',
        error: error.message,
      })
    }
  }

  public async update({ params, response, request, auth }: HttpContext) {
    await auth.authenticate()
    const validationData = await request.validateUsing(updateStoryValidator)

    try {
      const story = await Story.findOrFail(params.id)

      // Update story with validated data
      story.merge(validationData)
      await story.save()

      return response.ok({ success: true, data: story })
    } catch (error) {
      return response.badRequest({
        success: false,
        message: 'Failed to update story',
        error: error.message,
      })
    }
  }

  public async deleteStory({ params, response }: HttpContext) {
    const deleteStory = await Story.findOrFail(params.id)
    await deleteStory.delete()

    return response.ok({ success: true })
  }

  public async findOne({ auth, response, params }: HttpContext) {
    console.log('StoriesController findOne: ', params)
    const user = await User.find(auth.user?.id ?? 31)

    if (user === null) {
      const story = await Story.query()
        .where('id', params.id)
        .preload('previewVideo')
        .preload('previewImage')
        .preload('thumbnail')
        .preload('defaultChapter', (query) => {
          query.preload('video').preload('fallbackChapter')
        })
        .preload('chapters', (query) => {
          query.preload('video').preload('fallbackChapter')
        })
        .preload('tags')
        .first()

      if (story === null) {
        return response.notFound({
          message: 'Story not found',
        })
      }

      return response.ok({
        data: story,
      })
    }

    // For authenticated users, include additional data
    const story = await Story.query()
      .where('id', params.id)
      .preload('previewVideo')
      .preload('previewImage')
      .preload('thumbnail')
      .preload('defaultChapter', (query) => {
        query.preload('video').preload('fallbackChapter')
      })
      .preload('chapters', (query) => {
        query.preload('video').preload('fallbackChapter')
      })
      .preload('tags')
      .preload('storyOrders', (query) => query.where('user_id', user.id).where('blocked', false))
      .first()

    if (story === null) {
      return response.notFound({
        message: 'Story not found',
      })
    }

    return response.ok({
      data: story,
    })
  }

  public async adminFindOne({ params, response }: HttpContext) {
    const story = await Story.query()
      .where('id', params.id)
      .preload('chapters', (query) => {
        query.preload('video')
      })
      .preload('previewVideo')
      .preload('previewImage')
      .preload('thumbnail')
      .preload('tags')
      .first()

    if (story === null) {
      return response.notFound({
        message: 'Story not found',
      })
    }

    return response.ok({ data: story })
  }
}
