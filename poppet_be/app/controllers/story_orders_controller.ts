import type { HttpContext } from '@adonisjs/core/http'
import db from '@adonisjs/lucid/services/db'
import StoryOrder from '#models/story_order'
import Story, { StoryType } from '#models/story'
import User from '#models/user'
import { omit } from 'radash'
import { keys } from 'radash'

export default class StoryOrdersController {
  /**
   * Get all story orders with optional user context
   */
  async findAll({ auth, request, response }: HttpContext) {
    const user = await User.find(auth.user?.id ?? 0)
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = omit(request.all(), ['page', 'limit', 'sort'])

    if (!keys(filters).includes('region')) {
      filters['region'] = 'sg'
    }

    // If logged in return user-preloaded story orders
    // Otherwise return all story orders
    if (user === null) {
      const storyOrders = await StoryOrder.filter(filters)
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.status(200).send(storyOrders)
    }

    const storyOrders = await StoryOrder.filter(filters)
      .preload('story', (query) => {
        query.preload('preschool')
      })
      .preload('user')
      .preload('voucher')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.status(200).send(storyOrders)
  }

  /**
   * Get user story redemption for a specific story (admin view)
   */
  async findUserStoryRedemption({ request, response, params }: HttpContext) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'id:asc').split(':')
      const filters = omit(request.all(), ['page', 'limit', 'sort'])

      const storyOrders = await StoryOrder.filter(filters)
        .where('block', false)
        .whereHas('story', (query) => {
          query.where('id', params.id)
        })
        .preload('user')
        .preload('story')
        .preload('voucher')
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.status(200).send(storyOrders)
    } catch (error) {
      console.log(error)
      return response.status(400).send({
        success: false,
        error: error.message || 'Failed to fetch story redemptions',
      })
    }
  }

  /**
   * Get user preschool redemption for a specific preschool (admin view)
   */
  async findUserPreschoolRedemption({ request, response, params }: HttpContext) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const sort = request.input('sort', 'id:asc').split(':')
      const filters = omit(request.all(), ['page', 'limit', 'sort'])

      const storyOrders = await StoryOrder.filter(filters)
        .where('block', false)
        .whereHas('story', (query) => {
          query.whereHas('preschool', (query) => query.where('id', params.id))
        })
        .preload('user')
        .preload('story')
        .preload('voucher')
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.status(200).send(storyOrders)
    } catch (error) {
      console.log(error)
      return response.status(400).send({
        success: false,
        error: error.message || 'Failed to fetch preschool redemptions',
      })
    }
  }

  /**
   * Block or unblock user access to preschool stories
   */
  async blockUserPreschoolAccess({ request, response }: HttpContext) {
    try {
      const validationData = request.only(['user_id', 'preschool_id', 'blocked'])

      await db.transaction(async (trx) => {
        if (validationData.blocked) {
          const storyOrders = await StoryOrder.query()
            .where('user_id', validationData.user_id)
            .whereHas('story', (query) => query.where('preschool_id', validationData.preschool_id))

          for (let storyOrder of storyOrders) {
            storyOrder.blocked = true
            storyOrder.useTransaction(trx)
            await storyOrder.save()
          }
        } else {
          const stories = await Story.query()
            .where('preschool_id', validationData.preschool_id)
            .where('type', StoryType.PRESCHOOL)
            .where('status', 'active')

          await StoryOrder.updateOrCreateMany(
            ['storyId', 'userId'],
            stories.map((story) => ({
              storyId: story.id,
              userId: validationData.user_id,
              finalAmount: 0,
              discountAmount: 0,
              blocked: false,
            })),
            { client: trx }
          )
        }
      })

      return response.status(200).send({ success: true })
    } catch (error) {
      console.log(error)
      return response.status(400).send({
        success: false,
        error: error.message || 'Failed to update user access',
      })
    }
  }
}
