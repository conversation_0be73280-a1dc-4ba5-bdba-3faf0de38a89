import type { HttpContext } from '@adonisjs/core/http'
import db from '@adonisjs/lucid/services/db'
import Story from '#models/story'
import StoryRating, { StoryRatingStatus } from '#models/story_rating'
import { updateStoryRatingValidator } from '#validators/utility_validator'
import { omit } from 'radash'

export default class StoryRatingsController {
  /**
   * Get story ratings with filtering (admin view)
   */
  async findStoryRatings({ request, response }: HttpContext) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = omit(request.all(), ['page', 'limit', 'sort'])
    const userFilters = { email: filters.user_email }
    const storyFilters = { id: filters.story_id }

    const storyRatings = await StoryRating.filter(filters)
      .where('status', StoryRatingStatus.RATED)
      .whereHas('user', (query) => query.filter(userFilters))
      .whereHas('story', (query) => query.filter(storyFilters))
      .preload('story')
      .preload('user')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.ok(storyRatings)
  }

  /**
   * Get or create user's story rating
   */
  async findMyStoryRating({ response, auth, params }: HttpContext) {
    try {
      const user = await auth.authenticate()

      const story = await Story.findOrFail(params.story_id)
      let findStoryRating = await StoryRating.query()
        .where('user_id', user.id)
        .where('story_id', story.id)
        .first()

      await db.transaction(async (trx) => {
        if (findStoryRating) {
          const nextCount = findStoryRating.count + 1
          if (findStoryRating.status === StoryRatingStatus.ASKING && nextCount <= 4) {
            // nextCount = 2, 3 or 4
            if (nextCount <= 3) {
              // nextCount = 2, 3
              findStoryRating.count++
            } else {
              // nextCount = 4, but max count still remain 3
              findStoryRating.status = StoryRatingStatus.PENDING
            }
          }
        } else {
          findStoryRating = new StoryRating()
          findStoryRating.userId = user.id
          findStoryRating.storyId = story.id
          findStoryRating.count = 1 // initial count
          findStoryRating.status = StoryRatingStatus.ASKING
        }

        findStoryRating.useTransaction(trx)
        await findStoryRating.save()
      })

      return response.ok({ data: findStoryRating })
    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }

  /**
   * Update user's story rating
   */
  async updateMyStoryRating({ request, response, params, auth }: HttpContext) {
    try {
      const user = await auth.authenticate()
      const validationData = await request.validateUsing(updateStoryRatingValidator)

      const storyRating = await StoryRating.findOrFail(params.id)
      if (storyRating.userId !== user.id) {
        return response.badRequest({ success: false, message: 'Invalid access' })
      }

      await db.transaction(async (trx) => {
        storyRating.merge({ ...validationData, status: StoryRatingStatus.RATED })
        storyRating.useTransaction(trx)
        await storyRating.save()
      })

      if (validationData.rating) {
        return response.ok({ success: true, data: { follow_up: validationData.rating <= 3 } })
      }

      return response.ok({ success: true })
    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }
}
