import type { HttpContext } from '@adonisjs/core/http'
import Chat from '#models/chat'
import ChatHistory from '#models/chat_history'
import emitter from '@adonisjs/core/services/emitter'
import { DateTime } from 'luxon'
import { createChatValidator, createChatHistoryValidator } from '#validators/chat_validator'

export default class UserChatsController {
  /**
   * Get paginated list of user's chats
   */
  async find({ response, request, auth }: HttpContext) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'latest_message_date:desc').split(':')

    const result = await Chat.query()
      .where('user_id', auth.user!.id)
      .preload('user')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.ok(result)
  }

  /**
   * Get a specific chat for the authenticated user
   */
  async findOne({ response, params, auth }: HttpContext) {
    const result = await Chat.query()
      .where('id', params.id)
      .andWhere('user_id', auth.user!.id)
      .preload('user')
      .first()

    if (!result) {
      return response.notFound()
    }

    return response.ok(result)
  }

  /**
   * Create a new chat
   */
  async create({ response, request, auth }: HttpContext) {
    try {
      const payload = await request.validateUsing(createChatValidator)

      const chat = await Chat.create({ userId: payload.user_id })
      const history = new ChatHistory()

      history.merge({
        userId: auth.user!.id,
        content: payload.content,
        chatId: chat.id,
        seen: DateTime.now(),
        createdAt: chat.createdAt,
      })
      await history.save()

      emitter.emit('chat:create', chat.id)
      return response.created({ data: chat, success: true })
    } catch (e) {
      console.log(e)
      return response.badRequest(e)
    }
  }

  /**
   * Create a new chat history entry
   */
  async createHistory({ request, response, params, auth }: HttpContext) {
    try {
      const chat = await Chat.query()
        .where('id', params.id)
        .andWhere('user_id', auth.user!.id)
        .first()

      if (!chat) {
        return response.notFound()
      }

      const payload = await request.validateUsing(createChatHistoryValidator)

      const history = await ChatHistory.create({
        ...payload,
        userId: auth.user!.id,
        chatId: chat.id,
        seen: DateTime.local(), // user's message set to seen to avoid counted as unread
      })

      emitter.emit('history:create', history.id)
      return response.created({ data: history, success: true })
    } catch (e) {
      return response.badRequest(e)
    }
  }

  /**
   * Get paginated chat histories for a specific chat
   */
  async findHistories({ request, response, params, auth }: HttpContext) {
    const chat = await Chat.query()
      .where('id', params.id)
      .andWhere('user_id', auth.user!.id)
      .first()

    if (!chat) {
      return response.notFound()
    }

    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'latest_message_date:desc').split(':')

    const result = await ChatHistory.query()
      .preload('user')
      .where('chat_id', params.id)
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    emitter.emit('chat:seen', {
      historyIds: result.map((item) => item.id),
      chatId: chat.id,
    })

    return response.ok(result)
  }
}
