import type { HttpContext } from '@adonisjs/core/http'
import UserEvent from '#models/user_event'
import { omit } from 'radash'

export default class UserEventsController {
  /**
   * Get paginated list of user events with filtering
   */
  async findAll({ request, response }: HttpContext) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = omit(request.all(), ['page', 'sort', 'limit'])

    const userEvents = await UserEvent.filter(filters)
      .preload('user')
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.status(200).send(userEvents)
  }
}
