import type { HttpContext } from '@adonisjs/core/http'
import db from '@adonisjs/lucid/services/db'
import UserSetting from '#models/user_setting'
import { updateUserSettingValidator } from '#validators/utility_validator'

export default class UserSettingsController {
  /**
   * Get or create user settings
   */
  async findUserSetting({ response, auth }: HttpContext) {
    try {
      const user = await auth.authenticate()

      let userSettings
      await db.transaction(async (trx) => {
        userSettings = await UserSetting.firstOrCreate(
          { userId: user.id },
          {
            metadata: {
              repeat_animation: 0,
            },
          },
          { client: trx }
        )
      })

      return response.ok({ data: userSettings })
    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }

  /**
   * Update user settings
   */
  async updateUserSetting({ request, response, auth, params }: HttpContext) {
    try {
      const user = await auth.authenticate()
      const validationData = await request.validateUsing(updateUserSettingValidator)

      const userSetting = await UserSetting.findOrFail(params.id)
      if (userSetting.userId !== user.id) {
        return response.badRequest({ success: false, message: 'Invalid access' })
      }

      await db.transaction(async (trx) => {
        userSetting.merge({ ...validationData })
        userSetting.useTransaction(trx)
        await userSetting.save()
      })

      return response.ok({ success: true, data: userSetting })
    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }
}
