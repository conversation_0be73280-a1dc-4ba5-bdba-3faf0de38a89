import type { HttpContext } from '@adonisjs/core/http'
import db from '@adonisjs/lucid/services/db'
import { omit } from 'radash'

import User from '#models/user'
import {
  updateUserInformationValidator,
  adminChangePasswordValidator,
} from '#validators/user_validator'

export default class UsersController {
  public async find({ response, request }: HttpContext) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const filters = omit(request.all(), ['page', 'limit', 'sort'])

    const users = await User.filter(filters)
      .doesntHave('admin')
      .preload('userPacks')
      .preload('subscriptions')
      .preload('devices')
      .preload('wallet', (query) =>
        query.withCount('transactions', (query) => query.sum('amount').as('balance'))
      )
      .orderBy(sort[0], sort[1])
      .paginate(page, limit)

    return response.ok(users)
  }

  public async findOne({ response, params }: HttpContext) {
    const user = await User.query()
      .where('id', params.id)
      .preload('children')
      .preload('userPacks')
      .preload('transactions')
      .firstOrFail()

    return response.ok({
      data: user,
    })
  }

  public async findMyself({ auth, response }: HttpContext) {
    const myself = await auth.authenticate()

    // preload for other information
    const findMe = await User.query()
      .where('id', myself.id)
      .preload('children')
      .preload('userPacks')
      .preload('activeChild')
      .first()
    // .preload('userGroup')
    // .preload('userSetting')

    return response.ok({
      data: findMe,
    })
  }

  public async updateUserInformation({ auth, request, response }: HttpContext) {
    const user = await auth.authenticate()
    const validateData = await request.validateUsing(updateUserInformationValidator, {
      meta: {
        userId: user.id,
      },
    })

    user.merge(validateData)
    await user.save()

    return response.ok({
      success: true,
      data: user,
    })
  }

  // for admin use
  public async changePassword({ request, response }: HttpContext) {
    const validationData = await request.validateUsing(adminChangePasswordValidator)

    const findUser = await User.find(validationData.user_id)
    if (findUser === null) {
      return response.badRequest({
        success: false,
        message: 'Invalid user',
      })
    }

    findUser.password = validationData.password
    await findUser.save()

    return response.ok({
      success: true,
    })
  }

  public async deleteMe({ auth, response }: HttpContext) {
    const user = await User.findOrFail(auth?.user?.id)
    user.blocked = true
    await user.save()
    await auth.logout()
    await db.from('api_tokens').where('user_id', user.id).delete()
    return response.ok({
      success: true,
    })
  }
}
