import type { HttpContext } from '@adonisjs/core/http'
import db from '@adonisjs/lucid/services/db'
import Transaction from '#models/transaction'
import Wallet from '#models/wallet'
import { freeCreditsValidator } from '#validators/wallet_validator'
import { omit } from 'radash'

export default class WalletsController {
  /**
   * Get user's wallet with balance
   */
  async findMyWallet({ response, auth }: HttpContext) {
    try {
      const user = await auth.authenticate()
      const myWallet = await Wallet.query()
        .where('user_id', user.id)
        .withCount('transactions', (query) => {
          query.sum('amount').as('balance').where('status', 'confirmed')
        })
        .firstOrFail()

      return response.status(200).send(myWallet)
    } catch (error) {
      console.log(error)
      return response.status(400).send(error)
    }
  }

  /**
   * Get user's transaction history
   */
  async findMyTransactions({ request, response, auth }: HttpContext) {
    try {
      const user = await auth.authenticate()
      const wallet = await Wallet.findByOrFail('user_id', user.id)

      const page = request.input('page', 1)
      const limit = request.input('limit', 10)
      const sort = request.input('sort', 'id:desc').split(':')

      const transactions = await Transaction.query()
        .where('user_id', user.id)
        .where('wallet_id', wallet.id)
        .where('status', 'confirmed')
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.status(200).send(transactions)
    } catch (error) {
      console.log(error)
      return response.status(400).send(error)
    }
  }

  /**
   * Get all transactions (admin view)
   */
  async findTransactions({ request, response }: HttpContext) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 10)
      const sort = request.input('sort', 'id:desc').split(':')
      const filters = omit(request.all(), ['page', 'sort', 'limit'])

      const transactions = await Transaction.filter(filters)
        .preload('user')
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.status(200).send(transactions)
    } catch (error) {
      console.log(error)
      return response.status(400).send(error)
    }
  }

  /**
   * Give free credits to a user (admin only)
   */
  async freeCredits({ request, response }: HttpContext) {
    try {
      const validationData = await request.validateUsing(freeCreditsValidator)
      const wallet = await Wallet.findByOrFail('user_id', validationData.user_id)

      await db.transaction(async (trx) => {
        const sendFreeCredit = new Transaction()
        sendFreeCredit.walletId = wallet.id
        sendFreeCredit.userId = wallet.userId
        sendFreeCredit.title = 'Top-up'
        sendFreeCredit.description = 'Free Credit'
        sendFreeCredit.type = 'deposit'
        sendFreeCredit.status = 'confirmed'
        sendFreeCredit.amount = validationData.amount
        sendFreeCredit.amountIn = validationData.amount
        sendFreeCredit.amountOut = 0
        sendFreeCredit.remark = 'Free credit'

        sendFreeCredit.useTransaction(trx)
        await sendFreeCredit.save()
      })

      return response.ok({ success: true })
    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }
}
