import { DateTime } from 'luxon'
import { BaseModel, column } from '@adonisjs/lucid/orm'

// for email & sms verification
export default class Auth extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare email: string

  declare phone: string

  declare code: string

  declare status: number
  //0-new 1-used

  declare type: number
  //0-register,1-forgot_password,2-phone

  declare userId: number

  @column.dateTime()
  declare expiredAt: DateTime

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}
