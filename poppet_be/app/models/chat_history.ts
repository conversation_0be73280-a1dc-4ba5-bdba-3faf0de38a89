import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, BelongsTo, beforeFetch, beforePaginate } from '@adonisjs/lucid/orm'
import { compose } from '@adonisjs/core/helpers'
import { Filterable } from 'adonis-lucid-filter'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import Chat from './chat.js'
import User from './user.js'

export default class ChatHistory extends compose(BaseModel, Filterable) {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare chatId: number

  @column()
  declare userId: number

  @belongsTo(() => User, { foreignKey: 'userId' })
  declare user: BelongsTo<typeof User>

  @belongsTo(() => Chat, { foreignKey: 'chatId' })
  declare chat: BelongsTo<typeof Chat>

  @column({ serialize: (value) => !!value })
  declare isDeleted: boolean

  @column.dateTime()
  declare seen: DateTime

  @column()
  declare content: string

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @beforePaginate()
  static ignoreDeleteMultiplePaginate(
    queries: ModelQueryBuilderContract<typeof ChatHistory>[]
  ) {
    queries[0].andWhere('is_deleted', false)
    queries[1].andWhere('is_deleted', false)
  }

  @beforeFetch()
  static ignoreDeleteMultiple(query: ModelQueryBuilderContract<typeof ChatHistory>) {
    query.andWhere('is_deleted', false)
  }
}
