import { DateTime } from 'luxon'
import { BaseModel, column } from '@adonisjs/lucid/orm'

export default class Device extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: number

  declare deviceId: string

  declare platform: string

  declare model: string

  declare version: string

  declare pushToken: string

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  declare isActive: boolean

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships - Note: These will need to be uncommented as we migrate the related models
  // @belongsTo(() => User)
  // declare user: BelongsTo<typeof User>
}
