import { BaseModelFilter } from 'adonis-lucid-filter'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import Banner from '../banner.js'

export default class BannerFilter extends BaseModelFilter {
  declare $query: ModelQueryBuilderContract<typeof Banner>

  public id(value: any): void {
    this.$query.where('id', value)
  }

  public region(value: any): void {
    this.$query.where('region', value)
  }
}
