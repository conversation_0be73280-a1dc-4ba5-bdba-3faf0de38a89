import { BaseModelFilter } from 'adonis-lucid-filter'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import Pack from '#models/pack'

export default class PackFilter extends BaseModelFilter {
  declare $query: ModelQueryBuilderContract<typeof Pack>

  public title(value: any): void {
    this.$query.where('title', 'LIKE', `%${value}%`)
  }

  public region(value: any): void {
    this.$query.where('region', 'LIKE', `%${value}%`)
  }

  public language(value: any): void {
    this.$query.where('language', value)
  }

  public fromDate(value: any): void {
    this.$query.where('created_at', '>=', value)
  }

  public toDate(value: any): void {
    this.$query.where('created_at', '<=', value)
  }
}
