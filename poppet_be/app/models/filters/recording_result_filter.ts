import { BaseModelFilter } from 'adonis-lucid-filter'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import RecordingResult from '#models/recording_result'

export default class RecordingResultFilter extends BaseModelFilter {
  declare $query: ModelQueryBuilderContract<typeof RecordingResult>

  // TODO: Uncomment when User relationship is added to RecordingResult model
  // public email(value: any): void {
  //   this.$query.whereHas('user', (query) => query.where('email', 'LIKE', `%${value}%`))
  // }

  public id(value: any): void {
    this.$query.where('id', value)
  }

  public fromDate(value: any): void {
    this.$query.where('created_at', '>=', value)
  }

  public toDate(value: any): void {
    this.$query.where('created_at', '<=', value)
  }
}
