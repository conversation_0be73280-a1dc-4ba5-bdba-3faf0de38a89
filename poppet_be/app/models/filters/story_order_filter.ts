import { BaseModelFilter } from 'adonis-lucid-filter'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import StoryOrder from '../story_order.js'

export default class StoryOrderFilter extends BaseModelFilter {
  declare $query: ModelQueryBuilderContract<typeof StoryOrder, StoryOrder>

  public userId(value: any): void {
    this.$query.where('user_id', value)
  }

  public storyId(value: any): void {
    this.$query.where('story_id', value)
  }

  public blocked(value: any): void {
    this.$query.where('blocked', value)
  }
}
