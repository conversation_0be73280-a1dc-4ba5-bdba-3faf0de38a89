import { BaseModelFilter } from 'adonis-lucid-filter'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import Transaction from '../transaction.js'

export default class TransactionFilter extends BaseModelFilter {
  declare $query: ModelQueryBuilderContract<typeof Transaction, Transaction>

  public title(value: any): void {
    this.$query.where('title', value)
  }

  public type(value: any): void {
    this.$query.where('type', value)
  }

  public status(value: any): void {
    this.$query.where('status', value)
  }
}
