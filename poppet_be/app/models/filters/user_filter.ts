import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import User from '#models/user'

/**
 * UserFilter - Standalone filter utility for User model
 * Note: Cannot use BaseModelFilter due to conflicts with AuthFinder mixin
 */
export default class UserFilter {
  static apply(query: ModelQueryBuilderContract<typeof User>, filters: Record<string, any>) {
    Object.keys(filters).forEach(key => {
      const value = filters[key]
      if (value !== undefined && value !== null && value !== '') {
        switch (key) {
          case 'id':
            query.where('id', value)
            break
          case 'email':
            query.where('email', 'LIKE', `%${value}%`)
            break
          case 'name':
            query.where('name', 'LIKE', `%${value}%`)
            break
          case 'region':
            query.where('region', 'LIKE', `%${value}%`).orWhere('region', value)
            break
          case 'fromDate':
            query.where('created_at', '>=', value)
            break
          case 'toDate':
            query.where('created_at', '<=', value)
            break
        }
      }
    })
    return query
  }
}
