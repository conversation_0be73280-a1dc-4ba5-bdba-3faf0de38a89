import { BaseModelFilter } from 'adonis-lucid-filter'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import UserPack from '../user_pack.js'

export default class UserPackFilter extends BaseModelFilter {
  declare $query: ModelQueryBuilderContract<typeof UserPack, UserPack>

  public userId(value: any): void {
    this.$query.where('user_id', value)
  }

  public packId(value: any): void {
    this.$query.where('pack_id', value)
  }

  public blocked(value: any): void {
    this.$query.where('blocked', value)
  }
}
