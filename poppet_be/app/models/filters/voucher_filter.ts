import { BaseModelFilter } from 'adonis-lucid-filter'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import Voucher from '../voucher.js'

export default class VoucherFilter extends BaseModelFilter {
  declare $query: ModelQueryBuilderContract<typeof Voucher, Voucher>

  public code(value: any): void {
    this.$query.where('code', value)
  }

  public storyId(value: any): void {
    this.$query.where('story_id', value)
  }

  public blocked(value: any): void {
    this.$query.where('blocked', value)
  }
}
