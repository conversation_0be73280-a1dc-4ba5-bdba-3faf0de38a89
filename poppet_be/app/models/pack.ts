import { DateTime } from 'luxon'
import { BaseModel, column, hasMany, HasMany } from '@adonisjs/lucid/orm'
import { compose } from '@adonisjs/core/helpers'
import { Filterable } from 'adonis-lucid-filter'
import PackFilter from '#models/filters/pack_filter'

export default class Pack extends compose(BaseModel, Filterable) {
  static $filter = () => PackFilter
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare title: string

  @column()
  declare description: string | null

  @column()
  declare thumbnailUrl: string | null

  @column()
  declare featuredImage: string

  @column({ prepare: (value) => JSON.stringify(value) })
  declare region: string | Array<string>

  @column()
  declare language: string

  @column()
  declare noOfLevel: number

  @column()
  declare store_url: string

  @column({ prepare: (value) => JSON.stringify(value) })
  declare slides: Array<object> | string

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships
  @hasMany(() => import('./story.js').then(m => m.default), {
    serializeAs: 'stories',
  })
  declare stories: HasMany<typeof import('./story.js').default>

  @hasMany(() => import('./pack_level.js').then(m => m.default), {
    serializeAs: 'packLevels',
  })
  declare packLevels: HasMany<typeof import('./pack_level.js').default>

  @hasMany(() => import('./user_pack.js').then(m => m.default))
  declare userPacks: HasMany<typeof import('./user_pack.js').default>
}
