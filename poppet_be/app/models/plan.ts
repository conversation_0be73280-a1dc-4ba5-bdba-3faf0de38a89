import { DateTime } from 'luxon'
import { BaseModel, column } from '@adonisjs/lucid/orm'

export default class Plan extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare title: string

  declare description: string

  @column({ consume: (value: number) => Number(value) })
  declare price: number

  declare currency: string

  declare interval: string

  declare stripePriceId: string

  @column({
    consume: (value: boolean) => Boolean(value),
  })
  declare isActive: boolean

  declare isFeatured: boolean

  @column({ prepare: (value) => JSON.stringify(value) })
  declare features: Array<string>

  declare metadata: any

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships - Note: These will need to be uncommented as we migrate the related models
  // @manyToMany(() => Story, {
  //   pivotTable: 'plan_stories',
  //   pivotForeignKey: 'plan_id',
  //   pivotRelatedForeignKey: 'story_id',
  //   pivotColumns: ['level', 'is_featured', 'ordering', 'is_free'],
  //   pivotTimestamps: true,
  //   serializeAs: 'stories',
  // })
  // declare planStories: ManyToMany<typeof Story>

  // @hasMany(() => Subscription)
  // declare subscriptions: HasMany<typeof Subscription>
}
