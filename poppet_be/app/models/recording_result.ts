import { DateTime } from 'luxon'
import { BaseModel, column } from '@adonisjs/lucid/orm'

export default class RecordingResult extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare sessionId: number

  declare userId: number

  declare chapterId: number

  declare audioUrl: string

  declare transcript: string

  declare score: number

  declare maxScore: number

  @column({ prepare: (value) => JSON.stringify(value) })
  declare metadata: any

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships - Note: These will need to be uncommented as we migrate the related models
  // @belongsTo(() => Session)
  // declare session: BelongsTo<typeof Session>

  // @belongsTo(() => User)
  // declare user: BelongsTo<typeof User>

  // @belongsTo(() => Chapter)
  // declare chapter: BelongsTo<typeof Chapter>
}
