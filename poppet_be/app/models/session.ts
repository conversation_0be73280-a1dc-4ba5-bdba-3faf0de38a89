import { DateTime } from 'luxon'
import { BaseModel, column } from '@adonisjs/lucid/orm'

export default class Session extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare storyId: number

  declare gameScore: number

  declare fullGameScore: number

  declare userId: number

  declare childId: number

  @column.dateTime()
  declare endedAt: DateTime

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships - Note: These will need to be uncommented as we migrate the related models
  // @belongsTo(() => Story)
  // declare story: BelongsTo<typeof Story>

  // @belongsTo(() => User)
  // declare user: BelongsTo<typeof User>

  // @belongsTo(() => Child)
  // declare child: BelongsTo<typeof Child>

  // @hasMany(() => RecordingResult)
  // declare recordingResults: HasMany<typeof RecordingResult>
}
