import { DateTime } from 'luxon'
import { BaseModel, column } from '@adonisjs/lucid/orm'

export default class Subscription extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: number

  declare planId: number

  declare stripeSubscriptionId: string

  declare status: string

  @column({ consume: (value: number) => Number(value) })
  declare amount: number

  declare currency: string

  declare interval: string

  @column.dateTime()
  declare currentPeriodStart: DateTime

  declare currentPeriodEnd: DateTime

  declare canceledAt: DateTime | null

  declare endedAt: DateTime | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships - Note: These will need to be uncommented as we migrate the related models
  // @belongsTo(() => User)
  // declare user: BelongsTo<typeof User>

  // @belongsTo(() => Plan)
  // declare plan: BelongsTo<typeof Plan>
}
