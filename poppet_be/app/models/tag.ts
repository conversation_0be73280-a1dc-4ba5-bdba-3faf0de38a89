import { DateTime } from 'luxon'
import { BaseModel, column } from '@adonisjs/lucid/orm'

export default class Tag extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare name: string

  @column()
  declare slug: string

  @column()
  declare description: string | null

  @column()
  declare color: string | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships - Note: These will need to be uncommented as we migrate the related models
  // @manyToMany(() => Story, {
  //   pivotTable: 'story_tags',
  //   pivotTimestamps: true
  // })
  // declare stories: ManyToMany<typeof Story>
}
