import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column, beforeCreate } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { compose } from '@adonisjs/core/helpers'
import { Filterable } from 'adonis-lucid-filter'
import CryptoJS from 'crypto-js'

import User from './user.js'
import Wallet from './wallet.js'
import TransactionFilter from './filters/transaction_filter.js'

const randomNumber = (min: number, max: number) => {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

export default class Transaction extends compose(BaseModel, Filterable) {
  static $filter = () => TransactionFilter

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare walletId: number

  @column()
  declare userId: number

  @column()
  declare title: string

  @column()
  declare description: string

  @column()
  declare type: string

  @column()
  declare status: string

  @column({ consume: (value: number) => Number(value) })
  declare amount: number

  @column({ consume: (value: number) => Number(value) })
  declare amountIn: number

  @column({ consume: (value: number) => Number(value) })
  declare amountOut: number

  @column()
  declare remark: string

  @column()
  declare txnHash: string

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @belongsTo(() => Wallet)
  declare wallet: BelongsTo<typeof Wallet>

  @beforeCreate()
  static async updateField(txn: Transaction) {
    if (txn.$dirty.createdAt === null) {
      txn.createdAt = DateTime.local()
    }

    if (txn.$dirty.txnHash === null) {
      txn.txnHash = CryptoJS.SHA256(
        `${txn.walletId}${txn.userId}${txn.title}${txn.description}${txn.amount}${txn.createdAt
          .startOf('second')
          .toISO({ suppressMilliseconds: true })}${randomNumber(100000, 999999)}`
      ).toString()
    }
  }
}
