import { DateTime } from 'luxon'
import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { compose } from '@adonisjs/core/helpers'
import { Filterable } from 'adonis-lucid-filter'

import User from './user.js'
import UserEventFilter from './filters/user_event_filter.js'

export default class UserEvent extends compose(BaseModel, Filterable) {
  static $filter = () => UserEventFilter

  @column({ isPrimary: true })
  declare id: number

  @column()
  declare type: string
  // login, etc

  @column()
  declare location: string

  @column()
  declare ipAddress: string

  @column()
  declare device: string

  @column()
  declare provider: string

  @column()
  declare userId: number

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}
