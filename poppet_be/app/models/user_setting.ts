import { DateTime } from 'luxon'
import { BaseModel, column } from '@adonisjs/lucid/orm'

type UserSettingMetadata = {
  repeat_animation: number
}

export default class UserSetting extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column({
    consume: (value: boolean) => <PERSON><PERSON><PERSON>(value),
  })
  declare receiveUpdate: boolean

  @column()
  declare userId: number

  @column({ prepare: (values) => JSON.stringify(values) })
  declare metadata: UserSettingMetadata

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships - Note: These will need to be uncommented as we migrate the related models
  // @belongsTo(() => User)
  // declare user: BelongsTo<typeof User>
}
