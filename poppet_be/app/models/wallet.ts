import { DateTime } from 'luxon'
import { BaseModel, belongsTo, hasMany, column, computed } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import BigNumber from 'bignumber.js'

import User from './user.js'
import Transaction from './transaction.js'

export default class Wallet extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: number

  @column()
  declare currency: string

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @hasMany(() => Transaction)
  declare transactions: HasMany<typeof Transaction>

  @computed()
  get balance(): number {
    return new BigNumber(this.$extras.balance ?? 0).decimalPlaces(2).toNumber()
  }
}
