import vine from '@vinejs/vine'

/**
 * Validator for user registration
 */
export const registerValidator = vine.compile(
  vine.object({
    email: vine.string().email().trim(),
    password: vine.string().confirmed().trim(),
    auth_code: vine.string(),
    phone: vine.string().trim().optional(),
    name: vine.string().trim().optional(),
    region: vine.string().optional(),
  })
)

/**
 * Validator for user login
 */
export const loginValidator = vine.compile(
  vine.object({
    email: vine.string().email().trim().optional(),
    username: vine.string().trim().optional(),
    password: vine.string().trim(),
    ip_address: vine.string().optional().trim(),
    location: vine.string().optional().trim(),
  })
)

/**
 * Validator for magic link login
 */
export const loginMagicValidator = vine.compile(
  vine.object({
    email: vine.string().email().trim(),
    code: vine.string().trim(),
    region: vine.string().optional(),
    name: vine.string().optional(),
    child_name: vine.string().optional(),
    child_dob: vine.date().optional(),
  })
)

/**
 * Validator for forgot password
 */
export const forgotPasswordValidator = vine.compile(
  vine.object({
    email: vine.string().email().trim().maxLength(255),
    password: vine.string().confirmed().trim(),
    auth_code: vine.string(),
  })
)

/**
 * Validator for change password
 */
export const changePasswordValidator = vine.compile(
  vine.object({
    password: vine.string().trim(),
    new_password: vine.string().confirmed().trim(),
  })
)

/**
 * Validator for sending auth code
 */
export const sendAuthCodeValidator = vine.compile(
  vine.object({
    email: vine.string().email().trim(),
  })
)

/**
 * Validator for verifying auth code
 */
export const verifyAuthCodeValidator = vine.compile(
  vine.object({
    email: vine.string().email().trim(),
    code: vine.string().trim(),
  })
)
