import vine from '@vinejs/vine'

/**
 * Validator for creating a chapter
 */
export const createChapterValidator = vine.compile(
  vine.object({
    title: vine.string(),
    description: vine.string().optional(),
    story_id: vine.number(),
    options: vine.array(
      vine.object({
        value: vine.string(),
        chapterId: vine.number().optional(),
        answer: vine.boolean().optional(),
        threshold: vine.number(),
      })
    ).optional(),
    video_file: vine.file({
      size: '400mb',
      extnames: ['mp4'],
    }).optional(),
    video_url: vine.string().optional(),
    fallback_chapter_id: vine.number().optional(),
    is_free_response: vine.boolean(),
    audio_file: vine.file({
      extnames: ['mp3'],
      size: '10mb',
    }).optional(),
  })
)

/**
 * Validator for updating a chapter
 */
export const updateChapterValidator = vine.compile(
  vine.object({
    title: vine.string().optional(),
    description: vine.string().optional(),
    story_id: vine.number().optional(),
    options: vine.array(
      vine.object({
        value: vine.string(),
        chapterId: vine.number().optional(),
        answer: vine.boolean().optional(),
        threshold: vine.number(),
      })
    ).optional(),
    video_file: vine.file({
      size: '400mb',
      extnames: ['mp4'],
    }).optional(),
    video_url: vine.string().optional(),
    fallback_chapter_id: vine.number().optional(),
    is_free_response: vine.boolean().optional(),
    audio_file: vine.file({
      extnames: ['mp3'],
      size: '10mb',
    }).optional(),
  })
)
