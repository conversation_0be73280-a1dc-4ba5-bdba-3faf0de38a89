import vine from '@vinejs/vine'

/**
 * Validator for creating a new chat
 */
export const createChatValidator = vine.compile(
  vine.object({
    content: vine.string().trim(),
    user_id: vine.number().exists(async (db, value) => {
      const user = await db.from('users').where('id', value).first()
      return !!user
    }),
  })
)

/**
 * Validator for creating a new admin chat
 */
export const createAdminChatValidator = vine.compile(
  vine.object({
    content: vine.string().trim(),
    email: vine.string().email().exists(async (db, value) => {
      const user = await db.from('users').where('email', value).first()
      return !!user
    }),
  })
)

/**
 * Validator for creating chat history
 */
export const createChatHistoryValidator = vine.compile(
  vine.object({
    content: vine.string().trim(),
  })
)
