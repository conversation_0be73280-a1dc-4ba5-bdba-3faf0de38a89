import vine from '@vinejs/vine'

/**
 * Validator for updating credit information
 */
export const updateCreditValidator = vine.compile(
  vine.object({
    title: vine.string(),
    description: vine.string().optional(),
    amount: vine.number(),
    price: vine.number(),
    compare_at: vine.number(),
  })
)

/**
 * Validator for creating a credit
 */
export const createCreditValidator = vine.compile(
  vine.object({
    title: vine.string(),
    description: vine.string().optional(),
    handle: vine.string().optional(),
    amount: vine.number(),
    price: vine.number(),
    compare_at: vine.number(),
    currency: vine.string().optional(),
    rank: vine.number().optional(),
  })
)
