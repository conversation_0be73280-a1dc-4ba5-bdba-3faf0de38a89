import vine from '@vinejs/vine'

/**
 * Validator for general file upload
 */
export const uploadFileValidator = vine.compile(
  vine.object({
    file: vine.file({
      size: '400mb',
      extnames: [
        'pdf',
        'png',
        'jpg',
        'jpeg',
        'txt',
        'mp4',
        '3gp',
        'heic',
        'mp3',
        'wav',
        'mov',
        'm3u8',
      ],
    }),
    type: vine.enum(['image', 'video', 'audio', 'rive']),
    alt: vine.string().optional(),
  })
)

/**
 * Validator for user file upload
 */
export const uploadMyFileValidator = vine.compile(
  vine.object({
    file: vine.file({
      size: '400mb',
      extnames: ['pdf', 'png', 'jpg', 'jpeg', 'txt', 'mp4', '3gp', 'heic', 'mp3', 'wav', 'mov'],
    }),
    type: vine.string().optional(),
  })
)

/**
 * Validator for image files
 */
export const imageFileValidator = vine.compile(
  vine.object({
    image: vine.file({
      size: '20mb',
      extnames: ['png', 'jpg', 'jpeg'],
    }),
  })
)

/**
 * Validator for video files
 */
export const videoFileValidator = vine.compile(
  vine.object({
    video: vine.file({
      size: '400mb',
      extnames: ['mp4'],
    }),
  })
)

/**
 * Validator for audio files
 */
export const audioFileValidator = vine.compile(
  vine.object({
    audio: vine.file({
      size: '10mb',
      extnames: ['mp3', 'wav'],
    }),
  })
)

/**
 * Validator for recording files
 */
export const recordingFileValidator = vine.compile(
  vine.object({
    file: vine.file({
      size: '200mb',
      extnames: ['wav'],
    }),
    category: vine.array(vine.string().optional()),
    device: vine.string().optional(),
    user_token: vine.string().optional(),
    language: vine.string(),
    chapter_id: vine.number().optional(),
    story_id: vine.number().optional(),
    session_id: vine.number().optional(),
    calibration: vine.array(vine.string()),
    minimum_volume: vine.string(),
    actual_volume: vine.array(vine.string()),
    is_repeat: vine.boolean().optional(),
  })
)
