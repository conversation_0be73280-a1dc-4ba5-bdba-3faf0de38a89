import vine from '@vinejs/vine'

/**
 * Validator for creating a pack
 */
export const createPackValidator = vine.compile(
  vine.object({
    title: vine.string(),
    description: vine.string().optional(),
    thumbnail: vine.file({
      extnames: ['png', 'jpg', 'jpeg'],
      size: '20mb',
    }).optional(),
    region: vine.array(vine.string()),
    language: vine.string(),
    no_of_level: vine.number(),
  })
)

/**
 * Validator for updating a pack
 */
export const updatePackValidator = vine.compile(
  vine.object({
    title: vine.string().optional(),
    description: vine.string().optional(),
    thumbnail: vine.file({
      extnames: ['png', 'jpg', 'jpeg'],
      size: '20mb',
    }).optional(),
    is_thumbnail_removed: vine.boolean().optional(),
    region: vine.array(vine.string()).optional(),
    language: vine.string().optional(),
    no_of_level: vine.number().optional(),
    slides: vine.array(
      vine.object({
        title: vine.string(),
        description: vine.string(),
        src: vine.string().optional(),
        image: vine.file({
          extnames: ['png', 'jpg', 'jpeg'],
          size: '20mb',
        }).optional(),
      })
    ).optional(),
  })
)
