import vine from '@vinejs/vine'

/**
 * Validator for waived purchase
 */
export const waivedPurchaseValidator = vine.compile(
  vine.object({
    story_id: vine.number().exists(async (db, value) => {
      const story = await db.from('stories').where('id', value).first()
      return !!story
    }).optional(),
    preschool_id: vine.number().exists(async (db, value) => {
      const preschool = await db.from('preschools').where('id', value).first()
      return !!preschool
    }).optional(),
    voucher_id: vine.number().optional(),
  })
)

/**
 * Validator for Android purchase validation
 */
export const validateAndroidPurchaseValidator = vine.compile(
  vine.object({
    purchase_token: vine.string(),
    handle: vine.string().exists(async (db, value) => {
      const story = await db.from('stories').where('handle', value).where('is_community', true).first()
      return !!story
    }),
  })
)

/**
 * Validator for iOS purchase validation
 */
export const validateIOSPurchaseValidator = vine.compile(
  vine.object({
    transaction_id: vine.string(),
    handle: vine.string().exists(async (db, value) => {
      const story = await db.from('stories').where('handle', value).where('is_community', true).first()
      return !!story
    }),
  })
)

/**
 * Validator for Android credits purchase validation
 */
export const validateAndroidCreditsPurchaseValidator = vine.compile(
  vine.object({
    purchase_token: vine.string(),
    handle: vine.string().exists(async (db, value) => {
      const credit = await db.from('credits').where('handle', value).first()
      return !!credit
    }),
  })
)

/**
 * Validator for iOS credits purchase validation
 */
export const validateIOSCreditsPurchaseValidator = vine.compile(
  vine.object({
    transaction_id: vine.string(),
    handle: vine.string().exists(async (db, value) => {
      const credit = await db.from('credits').where('handle', value).first()
      return !!credit
    }),
  })
)

/**
 * Validator for purchase with credit
 */
export const purchaseWithCreditValidator = vine.compile(
  vine.object({
    bundle_id: vine.number().exists(async (db, value) => {
      const bundle = await db.from('bundles').where('id', value).first()
      return !!bundle
    }).optional(),
    story_id: vine.number().exists(async (db, value) => {
      const story = await db.from('stories').where('id', value).first()
      return !!story
    }).optional(),
  })
)
