import vine from '@vinejs/vine'

/**
 * Validator for creating a plan
 */
export const createPlanValidator = vine.compile(
  vine.object({
    title: vine.string(),
    handle: vine.string(),
    description: vine.string(),
    region: vine.array(vine.string()),
    language: vine.string(),
    blocked: vine.boolean().optional(),
    published: vine.boolean().optional(),
    thumbnail: vine.file({
      extnames: ['png', 'jpg', 'jpeg'],
      size: '20mb',
    }).optional(),
  })
)

/**
 * Validator for updating a plan
 */
export const updatePlanValidator = vine.compile(
  vine.object({
    title: vine.string().optional(),
    handle: vine.string().optional(),
    description: vine.string().optional(),
    language: vine.string().optional(),
    region: vine.array(vine.string().optional()).optional(),
    icon: vine.file({
      extnames: ['png', 'jpg', 'jpeg'],
      size: '20mb',
    }).optional(),
    thumbnail: vine.file({
      extnames: ['png', 'jpg', 'jpeg'],
      size: '20mb',
    }).optional(),
    type: vine.string().optional(), // Will need to define PlanType enum
    blocked: vine.boolean().optional(),
    published: vine.boolean().optional(),
  })
)
