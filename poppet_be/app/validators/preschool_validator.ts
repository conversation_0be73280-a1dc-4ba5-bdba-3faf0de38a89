import vine from '@vinejs/vine'

/**
 * Validator for creating a preschool
 */
export const createPreschoolValidator = vine.compile(
  vine.object({
    title: vine.string(),
    description: vine.string().optional(),
    region: vine.array(vine.string()),
    language: vine.string(),
    blocked: vine.boolean(),
  })
)

/**
 * Validator for updating a preschool
 */
export const updatePreschoolValidator = vine.compile(
  vine.object({
    title: vine.string(),
    description: vine.string().optional(),
    region: vine.array(vine.string()),
    language: vine.string(),
    blocked: vine.boolean(),
  })
)
