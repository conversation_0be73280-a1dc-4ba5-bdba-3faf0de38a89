import vine from '@vinejs/vine'

/**
 * Validator for creating recording results
 */
export const createRecordingResultValidator = vine.compile(
  vine.object({
    file: vine.file({
      size: '200mb',
      extnames: ['wav'],
    }).optional(),
    category: vine.array(vine.string().optional()),
    device: vine.string().optional(),
    user_token: vine.string().optional(),
    language: vine.string().optional(),
    chapter_id: vine.number(),
    calibration: vine.array(vine.string()),
    minimum_volume: vine.string(),
    actual_volume: vine.array(vine.string()),
  })
)
