import vine from '@vinejs/vine'

/**
 * Validator for storing recordings
 */
export const storeRecordingValidator = vine.compile(
  vine.object({
    file: vine.file({
      size: '200mb',
      extnames: ['wav'],
    }),
    category: vine.array(vine.string().optional()),
    device: vine.string().optional(),
    user_token: vine.string().optional(),
    language: vine.string(),
    chapter_id: vine.number().optional(),
    story_id: vine.number().optional(),
    session_id: vine.number().optional(),
    calibration: vine.array(vine.string()),
    minimum_volume: vine.string(),
    actual_volume: vine.array(vine.string()),
    is_repeat: vine.boolean().optional(),
  })
)

/**
 * Validator for instant store recordings
 */
export const instantStoreRecordingValidator = vine.compile(
  vine.object({
    file: vine.file({
      size: '200mb',
      extnames: ['wav'],
    }),
    category: vine.array(vine.string().optional()),
    device: vine.string().optional(),
    user_token: vine.string().optional(),
    language: vine.string(),
    chapter_id: vine.number().optional(),
    story_id: vine.number().optional(),
    session_id: vine.number().optional(),
    calibration: vine.array(vine.string()).optional(),
    minimum_volume: vine.string(),
    actual_volume: vine.array(vine.string()).optional(),
    is_repeat: vine.boolean().optional(),
  })
)

/**
 * Validator for finding recording results
 */
export const findRecordingValidator = vine.compile(
  vine.object({
    status_uri: vine.string(),
    recording_id: vine.number(),
    chapter_id: vine.number().optional(),
    story_id: vine.number().optional(),
    session_id: vine.number().optional(),
    calibrations: vine.array(vine.string()),
    minimum_volume: vine.string(),
    volumes: vine.array(vine.string()),
    device: vine.string().optional(),
  })
)

/**
 * Validator for instant find recording results
 */
export const instantFindRecordingValidator = vine.compile(
  vine.object({
    status_uri: vine.string(),
    recording_id: vine.number(),
    chapter_id: vine.number().optional(),
    story_id: vine.number().optional(),
    session_id: vine.number().optional(),
    calibrations: vine.array(vine.string()).optional(),
    minimum_volume: vine.string(),
    volumes: vine.array(vine.string()).optional(),
    device: vine.string().optional(),
  })
)
