import vine from '@vinejs/vine'

/**
 * Validator for creating a story
 */
export const createStoryValidator = vine.compile(
  vine.object({
    title: vine.string(),
    description: vine.string().optional(),
    language: vine.string(),
    level: vine.number(),
    region: vine.array(vine.string()),
    thumbnail: vine.file({
      extnames: ['png', 'jpg', 'jpeg'],
      size: '20mb',
    }),
    preview_image: vine.file({
      extnames: ['png', 'jpg', 'jpeg'],
      size: '20mb',
    }),
    preview_video: vine.file({
      size: '400mb',
      extnames: ['mp4'],
    }),
    default_chapter_id: vine.number().optional(),
    is_community: vine.boolean().optional(),
    tags: vine.array(vine.number()).optional(),
    word_count: vine.number().optional(),
    status: vine.enum(['active', 'upcoming', 'closed']),
    price: vine.number(),
  })
)

/**
 * Validator for updating a story
 */
export const updateStoryValidator = vine.compile(
  vine.object({
    title: vine.string().optional(),
    description: vine.string().optional(),
    language: vine.string().optional(),
    level: vine.number().optional(),
    region: vine.array(vine.string()).optional(),
    thumbnail: vine.file({
      extnames: ['png', 'jpg', 'jpeg'],
      size: '20mb',
    }).optional(),
    is_thumbnail_removed: vine.boolean().optional(),
    preview_image: vine.file({
      extnames: ['png', 'jpg', 'jpeg'],
      size: '20mb',
    }).optional(),
    is_preview_image_removed: vine.boolean().optional(),
    preview_video: vine.file({
      size: '400mb',
      extnames: ['mp4'],
    }).optional(),
    is_preview_video_removed: vine.boolean().optional(),
    default_chapter_id: vine.number().optional(),
    is_community: vine.boolean().optional(),
    tags: vine.array(vine.number()).optional(),
    word_count: vine.number().optional(),
    status: vine.enum(['active', 'upcoming', 'closed']).optional(),
    price: vine.number().optional(),
  })
)
