import vine from '@vinejs/vine'

/**
 * Validator for updating user information
 */
export const updateUserInformationValidator = vine
  .withMetaData<{ userId: number }>()
  .compile(
    vine.object({
      region: vine.string().optional(),
      active_child_id: vine.number(),
    })
  )

/**
 * Validator for admin change password
 */
export const adminChangePasswordValidator = vine.compile(
  vine.object({
    user_id: vine.number(),
    password: vine.string().trim(),
  })
)
