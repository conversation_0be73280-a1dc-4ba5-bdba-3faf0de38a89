import vine from '@vinejs/vine'

/**
 * Validator for creating device tokens
 */
export const createDeviceTokenValidator = vine.compile(
  vine.object({
    token: vine.string().trim().optional(),
    platform: vine.enum(['ios', 'android', 'huawei']),
    device_no: vine.string().trim(),
    model: vine.string().trim(),
  })
)

/**
 * Validator for creating settings
 */
export const createSettingValidator = vine.compile(
  vine.object({
    name: vine.string(),
    description: vine.string(),
    handle: vine.string(),
    status: vine.string(),
    value: vine.string(),
    type: vine.string(),
  })
)

/**
 * Validator for updating settings
 */
export const updateSettingValidator = vine.compile(
  vine.object({
    name: vine.string().optional(),
    handle: vine.string().optional(),
    description: vine.string().optional(),
    status: vine.string().optional(),
    value: vine.string().optional(),
    type: vine.string().optional(),
    metadata: vine.object().optional(),
  })
)

/**
 * Validator for user settings
 */
export const updateUserSettingValidator = vine.compile(
  vine.object({
    metadata: vine.object({
      repeat_animation: vine.number().range([0, 1]),
    }),
  })
)

/**
 * Validator for creating versions
 */
export const createVersionValidator = vine.compile(
  vine.object({
    platform: vine.string().trim(),
    build_version: vine.string().trim(),
    build_number: vine.number(),
    release_note: vine.string().trim(),
    update: vine.boolean(),
    app_url: vine.string().trim(),
    published_at: vine.date(),
  })
)

/**
 * Validator for updating versions
 */
export const updateVersionValidator = vine.compile(
  vine.object({
    platform: vine.string().trim(),
    build_version: vine.string().trim(),
    build_number: vine.number(),
    release_note: vine.string().trim(),
    update: vine.boolean(),
    app_url: vine.string().trim(),
    published_at: vine.date(),
  })
)

/**
 * Validator for finding latest version by platform
 */
export const findLatestVersionValidator = vine.compile(
  vine.object({
    platform: vine.enum(['android', 'ios']),
  })
)

/**
 * Validator for creating reports
 */
export const createReportValidator = vine.compile(
  vine.object({
    region: vine.string(),
    report_type: vine.enum([
      'user_masterlist',
      'user_logins',
      'recordings',
      'purchase_clicks',
      'redemption',
      'credits_purchased',
    ]),
  })
)

/**
 * Validator for creating reviews
 */
export const createReviewValidator = vine.compile(
  vine.object({
    title: vine.string().optional(),
    content: vine.string().optional(),
    score: vine.number(),
    more_activity: vine.boolean(),
    activity_id: vine.number().exists(async (db, value) => {
      const activity = await db.from('activities').where('id', value).first()
      return !!activity
    }),
    user_id: vine.number().exists(async (db, value) => {
      const user = await db.from('users').where('id', value).first()
      return !!user
    }).optional(),
  })
)

/**
 * Validator for updating reviews
 */
export const updateReviewValidator = vine.compile(
  vine.object({
    title: vine.string().optional(),
    content: vine.string().optional(),
    score: vine.number().optional(),
    more_activity: vine.boolean().optional(),
    user_id: vine.number().exists(async (db, value) => {
      const user = await db.from('users').where('id', value).first()
      return !!user
    }),
  })
)

/**
 * Validator for updating story ratings
 */
export const updateStoryRatingValidator = vine.compile(
  vine.object({
    rating: vine.number().range([1, 5]).optional(),
    review: vine.string().optional(),
    reason: vine.object({
      selected_options: vine.array(vine.string()).optional(),
      others: vine.string().optional(),
    }).optional(),
  })
)
