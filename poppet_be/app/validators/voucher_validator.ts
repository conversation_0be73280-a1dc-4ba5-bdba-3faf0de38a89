import vine from '@vinejs/vine'

/**
 * Validator for creating a voucher
 */
export const createVoucherValidator = vine.compile(
  vine.object({
    code: vine.string().optional(),
    description: vine.string().optional(),
    discount: vine.number(),
    story_id: vine.number().optional(),
    preschool_id: vine.number().optional(),
    max_of_used: vine.number().optional(),
    expired_at: vine.date().optional(),
  })
)

/**
 * Validator for verifying a voucher
 */
export const verifyVoucherValidator = vine.compile(
  vine.object({
    code: vine.string(),
    story_id: vine.string().optional(),
    preschool_id: vine.string().optional(),
  })
)

/**
 * Validator for updating a voucher
 */
export const updateVoucherValidator = vine.compile(
  vine.object({
    code: vine.string().optional(),
    description: vine.string().optional(),
    discount: vine.number().optional(),
    story_id: vine.number().optional(),
    preschool_id: vine.number().optional(),
    max_of_used: vine.number().optional(),
    expired_at: vine.date().optional(),
  })
)
