const fs = require('fs')
const path = require('path')

const modelsDir = path.join(__dirname, 'app/models')

// Get all TypeScript files in the models directory
const modelFiles = fs.readdirSync(modelsDir).filter(file => file.endsWith('.ts'))

modelFiles.forEach(file => {
  const filePath = path.join(modelsDir, file)
  let content = fs.readFileSync(filePath, 'utf8')
  
  // Remove all type imports for relations since they're only used in comments
  content = content.replace(/import type { [^}]+ } from '@adonisjs\/lucid\/types\/relations'\n/g, '')
  
  // Remove duplicate lines
  const lines = content.split('\n')
  const uniqueLines = []
  const seen = new Set()
  
  for (const line of lines) {
    if (!seen.has(line.trim()) || line.trim() === '') {
      uniqueLines.push(line)
      seen.add(line.trim())
    }
  }
  
  content = uniqueLines.join('\n')
  
  fs.writeFileSync(filePath, content)
  console.log(`Cleaned imports in ${file}`)
})

console.log('All model imports cleaned!')
