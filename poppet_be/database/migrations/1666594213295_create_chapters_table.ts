import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'chapters'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.string('title')
      table.string('handle')
      table.text('description')
      table.integer('story_id').unsigned().references('id').inTable('stories').onDelete('CASCADE')
      table.boolean('is_free_response').defaultTo(false)
      table.string('audio_url').nullable()
      table.integer('file_id').unsigned().references('id').inTable('files')
      table.json('options')
      table.integer('fallback_chapter_id').unsigned().references('id').inTable('chapters').nullable()

      /**
       * Uses timestampz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at')
      table.timestamp('updated_at')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
