import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'stories'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.integer('word_count')
      table.decimal('price', 8, 2)
      table.string('status').defaultTo('active')
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('word_count')
      table.dropColumn('price')
      table.dropColumn('status')
    })
  }
}
