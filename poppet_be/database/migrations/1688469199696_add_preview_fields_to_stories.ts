import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'stories'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('preview_image_url')
      table.integer('preview_video_id').unsigned().references('id').inTable('files')
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('preview_image_url')
      table.dropForeign('preview_video_id')
      table.dropColumn('preview_video_id')
    })
  }
}
