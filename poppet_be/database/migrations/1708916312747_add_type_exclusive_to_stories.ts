import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'stories'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.enum('type', ['community', 'game', 'pack', 'preschool'])
      table.boolean('is_exclusive').defaultTo(false)
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('type')
      table.dropColumn('is_exclusive')
    })
  }
}
