import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'plan_stories'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.integer('plan_id').unsigned().references('id').inTable('plans').onDelete('SET NULL')
      table.integer('story_id').unsigned().references('id').inTable('stories').onDelete('SET NULL')
      table.integer('level')
      table.boolean('is_featured')
      table.integer('ordering')
      table.unique(['plan_id', 'story_id'])

      /**
       * Uses timestampz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at')
      table.timestamp('updated_at')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
