const fs = require('fs')
const path = require('path')

const modelsDir = path.join(__dirname, 'app/models')

// Get all TypeScript files in the models directory
const modelFiles = fs.readdirSync(modelsDir).filter(file => file.endsWith('.ts'))

modelFiles.forEach(file => {
  const filePath = path.join(modelsDir, file)
  let content = fs.readFileSync(filePath, 'utf8')
  
  // Skip user.ts as it's already fixed
  if (file === 'user.ts') return
  
  // Fix imports - remove unused relationship type imports and add type import
  content = content.replace(
    /import { BaseModel,([^}]+) } from '@adonisjs\/lucid\/orm'/g,
    (match, imports) => {
      // Remove relationship types from the main import
      const cleanImports = imports
        .split(',')
        .map(imp => imp.trim())
        .filter(imp => !['BelongsTo', 'HasMany', 'HasOne', 'ManyToMany', 'belongsTo', 'hasMany', 'hasOne', 'manyToMany'].includes(imp))
        .join(', ')
      
      return `import { BaseModel${cleanImports ? ', ' + cleanImports : ''} } from '@adonisjs/lucid/orm'`
    }
  )
  
  // Add type import for relationship types if they're used in comments
  if (content.includes('BelongsTo') || content.includes('HasMany') || content.includes('HasOne') || content.includes('ManyToMany')) {
    const typesToImport = []
    if (content.includes('BelongsTo')) typesToImport.push('BelongsTo')
    if (content.includes('HasMany')) typesToImport.push('HasMany')
    if (content.includes('HasOne')) typesToImport.push('HasOne')
    if (content.includes('ManyToMany')) typesToImport.push('ManyToMany')
    
    // Add the type import after the BaseModel import
    content = content.replace(
      /import { BaseModel[^}]* } from '@adonisjs\/lucid\/orm'/,
      match => match + `\nimport type { ${typesToImport.join(', ')} } from '@adonisjs/lucid/types/relations'`
    )
  }
  
  fs.writeFileSync(filePath, content)
  console.log(`Fixed imports in ${file}`)
})

console.log('All model imports fixed!')
