/**
 * Migration utilities to help convert AdonisJS v5 models and migrations to v6
 */

import { nanoid } from 'nanoid'

/**
 * Generate a unique slug for a given string
 * This replaces the functionality of @adonisjs/lucid-slugify
 */
export function generateSlug(text: string, options: {
  strategy?: 'simple' | 'dbIncrement'
  separator?: string
} = {}): string {
  const { separator = '-' } = options

  return text
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, separator) // Replace spaces and underscores with separator
    .replace(/^-+|-+$/g, '') // Remove leading/trailing separators
}

/**
 * Generate a unique slug with random suffix for database increment strategy
 */
export function generateUniqueSlug(text: string, suffix?: string): string {
  const baseSlug = generateSlug(text)
  const uniqueSuffix = suffix || nanoid(6).toLowerCase()
  return `${baseSlug}-${uniqueSuffix}`
}

/**
 * Convert v5 import statements to v6 equivalents
 */
export const importMappings = {
  // Lucid ORM imports
  '@ioc:Adonis/Lucid/Orm': '@adonisjs/lucid/orm',
  '@ioc:Adonis/Lucid/Schema': '@adonisjs/lucid/schema',

  // Core imports
  '@ioc:Adonis/Core/Hash': '@adonisjs/core/hash',
  '@ioc:Adonis/Core/Helpers': '@adonisjs/core/helpers',

  // Database
  '@ioc:Adonis/Lucid/Database': '@adonisjs/lucid/services/db',

  // Auth (will need to be updated based on v6 auth structure)
  '@ioc:Adonis/Addons/Auth': '@adonisjs/auth/services/auth',
}

/**
 * Convert v5 model property declarations to v6
 */
export function convertPropertyDeclaration(line: string): string {
  // Convert "public property: type" to "declare property: type"
  return line.replace(/^\s*public\s+/, '  declare ')
}

/**
 * Convert v5 migration method signatures to v6
 */
export function convertMigrationMethod(line: string): string {
  // Remove "public" keyword from async methods
  return line.replace(/^\s*public\s+(async\s+(?:up|down)\(\))/, '  $1')
}

/**
 * Model conversion helpers
 */
export const modelConversions = {
  // Remove Filterable compose and related imports
  removeFilterable: (content: string): string => {
    return content
      .replace(/import.*Filterable.*from.*LucidFilter.*\n/g, '')
      .replace(/compose\(BaseModel,\s*Filterable\)/g, 'BaseModel')
      .replace(/public static \$filter = \(\) => \w+Filter\n/g, '')
  },

  // Convert slugify decorators to manual implementation
  convertSlugify: (content: string): string => {
    // This will need manual handling for each model
    return content.replace(/@slugify\({[\s\S]*?}\)/g, '// TODO: Implement manual slug generation')
  },

  // Convert property declarations
  convertProperties: (content: string): string => {
    return content.replace(/^\s*public\s+(\w+):/gm, '  declare $1:')
  }
}

/**
 * Migration conversion helpers
 */
export const migrationConversions = {
  // Convert import statements
  convertImports: (content: string): string => {
    return content.replace(
      /import BaseSchema from '@ioc:Adonis\/Lucid\/Schema'/g,
      "import { BaseSchema } from '@adonisjs/lucid/schema'"
    )
  },

  // Convert method signatures
  convertMethods: (content: string): string => {
    return content
      .replace(/public async up\(\)/g, 'async up()')
      .replace(/public async down\(\)/g, 'async down()')
  },

  // Convert timestamp methods
  convertTimestamps: (content: string): string => {
    return content
      .replace(/table\.timestamps\(true, true\)/g, 'table.timestamp(\'created_at\').notNullable()\n      table.timestamp(\'updated_at\').nullable()')
      .replace(/table\.timestamp\('(\w+)', { useTz: true }\)/g, 'table.timestamp(\'$1\')')
  }
}

/**
 * List of all models that need to be migrated
 */
export const modelsToMigrate = [
  'Admin', 'Auth', 'AzureAuth', 'Banner', 'Bundle', 'BundleStory',
  'Chapter', 'Chat', 'ChatHistory', 'Child', 'Credit', 'Device',
  'Feedback', 'FeedbackHistory', 'File', 'Pack', 'PackCode', 'PackLevel',
  'Plan', 'PlanPricing', 'Preschool', 'PurchaseClickTracker', 'PushNotification',
  'RecordingResult', 'Review', 'Session', 'Setting', 'Story', 'StoryOrder',
  'StoryRating', 'Subscription', 'Tag', 'Transaction', 'User', 'UserEvent',
  'UserGroup', 'UserPack', 'UserSetting', 'Version', 'Voucher', 'Wallet'
]

/**
 * Helper to create basic filter functionality using query builder
 */
export function createBasicFilter(modelName: string): string {
  return `
/**
 * Basic filtering functionality for ${modelName}
 * Replaces the v5 Filterable functionality
 */
export class ${modelName}Filter {
  static apply(query: any, filters: Record<string, any>) {
    Object.keys(filters).forEach(key => {
      const value = filters[key]
      if (value !== undefined && value !== null && value !== '') {
        // Add basic where conditions
        // This needs to be customized per model based on the original filter
        query.where(key, value)
      }
    })
    return query
  }
}
`
}
