import { test } from '@japa/runner'
import testUtils from '@adonisjs/core/services/test_utils'

test.group('Auth Controller', (group) => {
  group.each.setup(() => testUtils.db().truncate())

  test('should register a new user', async ({ client }) => {
    const response = await client.post('/api/v1/auth/register').json({
      email: '<EMAIL>',
      password: 'password123',
      password_confirmation: 'password123',
      auth_code: 'TEST123',
      name: 'Test User',
      region: 'sg',
    })

    response.assertStatus(200)
    response.assertBodyContains({
      success: true,
    })
  })

  test('should login with valid credentials', async ({ client }) => {
    // First register a user
    await client.post('/api/v1/auth/register').json({
      email: '<EMAIL>',
      password: 'password123',
      password_confirmation: 'password123',
      auth_code: 'TEST123',
      name: 'Test User',
      region: 'sg',
    })

    // Then try to login
    const response = await client.post('/api/v1/auth/login').json({
      email: '<EMAIL>',
      password: 'password123',
    })

    response.assertStatus(200)
    response.assertBodyContains({
      success: true,
    })
  })

  test('should fail login with invalid credentials', async ({ client }) => {
    const response = await client.post('/api/v1/auth/login').json({
      email: '<EMAIL>',
      password: 'wrongpassword',
    })

    response.assertStatus(400)
  })

  test('should send forgot password email', async ({ client }) => {
    // First register a user
    await client.post('/api/v1/auth/register').json({
      email: '<EMAIL>',
      password: 'password123',
      password_confirmation: 'password123',
      auth_code: 'TEST123',
      name: 'Test User',
      region: 'sg',
    })

    const response = await client.post('/api/v1/auth/forgot-password').json({
      email: '<EMAIL>',
    })

    response.assertStatus(200)
    response.assertBodyContains({
      success: true,
    })
  })
})
