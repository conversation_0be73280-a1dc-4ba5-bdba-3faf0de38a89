import { test } from '@japa/runner'
import testUtils from '@adonisjs/core/services/test_utils'

test.group('Banners Controller', (group) => {
  group.each.setup(() => testUtils.db().truncate())

  test('should get banners with default region filter', async ({ client }) => {
    const response = await client.get('/api/v1/banners')

    response.assertStatus(200)
    response.assertBodyContains({
      meta: {},
      data: [],
    })
  })

  test('should get banners with custom region filter', async ({ client }) => {
    const response = await client.get('/api/v1/banners?region=us')

    response.assertStatus(200)
    response.assertBodyContains({
      meta: {},
      data: [],
    })
  })

  test('should create a new banner (admin)', async ({ client }) => {
    // This would require admin authentication in a real scenario
    const response = await client.post('/api/v1/admin/banners').json({
      thumbnail_url: 'https://example.com/banner.jpg',
      cta_url: 'https://example.com/cta',
      region: 'sg',
    })

    // This will fail without proper authentication, but tests the route exists
    response.assertStatus(401) // Unauthorized without auth
  })

  test('should update a banner (admin)', async ({ client }) => {
    // This would require admin authentication in a real scenario
    const response = await client.put('/api/v1/admin/banners/1').json({
      thumbnail_url: 'https://example.com/updated-banner.jpg',
      cta_url: 'https://example.com/updated-cta',
      region: 'us',
    })

    // This will fail without proper authentication, but tests the route exists
    response.assertStatus(401) // Unauthorized without auth
  })

  test('should delete a banner (admin)', async ({ client }) => {
    // This would require admin authentication in a real scenario
    const response = await client.delete('/api/v1/admin/banners/1')

    // This will fail without proper authentication, but tests the route exists
    response.assertStatus(401) // Unauthorized without auth
  })
})
