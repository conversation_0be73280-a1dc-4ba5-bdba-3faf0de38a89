import { test } from '@japa/runner'
import testUtils from '@adonisjs/core/services/test_utils'

test.group('Files Controller', (group) => {
  group.each.setup(() => testUtils.db().truncate())

  test('should require authentication for file upload', async ({ client }) => {
    const response = await client.post('/api/v1/files/upload')

    response.assertStatus(401) // Unauthorized without auth
  })

  test('should require authentication for my file upload', async ({ client }) => {
    const response = await client.post('/api/v1/files/upload-my-file')

    response.assertStatus(401) // Unauthorized without auth
  })

  // Note: Testing actual file uploads would require more complex setup
  // with mock files and proper authentication. These tests verify the
  // routes exist and require authentication.
})
