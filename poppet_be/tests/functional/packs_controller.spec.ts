import { test } from '@japa/runner'
import testUtils from '@adonisjs/core/services/test_utils'

test.group('Packs Controller', (group) => {
  group.each.setup(() => testUtils.db().truncate())

  test('should get all packs without authentication', async ({ client }) => {
    const response = await client.get('/api/v1/packs')

    response.assertStatus(200)
    response.assertBodyContains({
      meta: {},
      data: [],
    })
  })

  test('should get all packs with region filter', async ({ client }) => {
    const response = await client.get('/api/v1/packs?region=us')

    response.assertStatus(200)
    response.assertBodyContains({
      meta: {},
      data: [],
    })
  })

  test('should get a single pack by ID', async ({ client }) => {
    const response = await client.get('/api/v1/packs/1')

    // This might return 404 if pack doesn't exist, which is expected
    response.assertStatus([200, 404])
  })

  test('should require authentication for my packs', async ({ client }) => {
    const response = await client.get('/api/v1/my-packs')

    response.assertStatus(401) // Unauthorized without auth
  })

  test('should require authentication for user packs', async ({ client }) => {
    const response = await client.get('/api/v1/user-packs')

    response.assertStatus(401) // Unauthorized without auth
  })

  test('should get admin packs (requires admin auth)', async ({ client }) => {
    const response = await client.get('/api/v1/admin/packs')

    response.assertStatus(401) // Unauthorized without auth
  })
})
