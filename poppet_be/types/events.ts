/**
 * Define custom events for the application
 */
declare module '@adonisjs/core/types' {
  interface EventsList {
    // Chat events
    'chat:create': number
    'history:create': number
    'chat:seen': { historyIds: number[]; chatId: number }
    
    // Webhook events
    'webhook:wix': { email: string }
    
    // Activity events
    'activity:start': { email: string }
    
    // Notification events
    'send:messages': {
      users: any[]
      title: string
      body: string
      data?: any
    }
    
    // Stripe events (if needed)
    'stripe:customer-created': any
    'stripe:customer-updated': any
    'stripe:subscription-created': any
    'stripe:subscription-updated': any
    'stripe:subscription-deleted': any
    'stripe:product-created': any
    'stripe:price-created': any
    'stripe:product-updated': any
    'stripe:price-updated': any
    'stripe:plan-purchased': any
    'stripe:plan-paused': any
    'stripe:plan-failed': any
    'plan:trial': { email: string }
  }
}
